# Plagiarism Detector - Deployment Guide

## Prerequisites

### System Requirements
- **Node.js**: Version 18.0 or higher
- **npm**: Version 8.0 or higher (comes with Node.js)
- **Memory**: Minimum 2GB RAM (4GB recommended for local models)
- **Storage**: 1GB free space for dependencies and models
- **Network**: Internet connection for OpenAI API and package downloads

### Required API Keys
- **OpenAI API Key**: Required for text-embedding-ada-002 model
  - Sign up at [OpenAI Platform](https://platform.openai.com/)
  - Generate API key from the API Keys section
  - Ensure sufficient credits for embedding usage

## Local Development Setup

### 1. Clone and Setup
```bash
# Clone the repository
git clone <repository-url>
cd plagiarism-detector

# Install backend dependencies
cd backend
npm install

# Install frontend dependencies
cd ../frontend
npm install
```

### 2. Environment Configuration
Create a `.env` file in the backend directory:

```env
# Required Configuration
OPENAI_API_KEY=your_openai_api_key_here
NODE_ENV=development

# Optional Configuration
PORT=5000
CORS_ORIGIN=http://localhost:3000
LOG_LEVEL=info

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=10
RATE_LIMIT_ANALYZE_MAX=5

# Model Configuration
DEFAULT_EMBEDDING_MODEL=text-embedding-ada-002
ENABLE_LOCAL_MODELS=true
MODEL_CACHE_SIZE=100
```

### 3. Start Development Servers
```bash
# Terminal 1 - Backend
cd backend
npm run dev

# Terminal 2 - Frontend
cd frontend
npm run dev
```

The application will be available at:
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:5000
- **API Documentation**: http://localhost:5000/health

## Production Deployment

### Option 1: Traditional Server Deployment

#### 1. Server Preparation
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
sudo npm install -g pm2

# Create application user
sudo useradd -m -s /bin/bash plagiarism-detector
sudo mkdir -p /opt/plagiarism-detector
sudo chown plagiarism-detector:plagiarism-detector /opt/plagiarism-detector
```

#### 2. Application Deployment
```bash
# Switch to application user
sudo su - plagiarism-detector

# Clone and setup application
cd /opt/plagiarism-detector
git clone <repository-url> .

# Install dependencies
cd backend && npm ci --production
cd ../frontend && npm ci

# Build frontend
npm run build
```

#### 3. Environment Configuration
```bash
# Create production environment file
cat > /opt/plagiarism-detector/backend/.env << EOF
NODE_ENV=production
PORT=5000
OPENAI_API_KEY=your_production_api_key
CORS_ORIGIN=https://yourdomain.com
LOG_LEVEL=warn
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX=20
RATE_LIMIT_ANALYZE_MAX=10
EOF

# Set proper permissions
chmod 600 /opt/plagiarism-detector/backend/.env
```

#### 4. Process Management with PM2
```bash
# Create PM2 ecosystem file
cat > /opt/plagiarism-detector/ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'plagiarism-detector-api',
    script: './backend/dist/server.js',
    cwd: '/opt/plagiarism-detector',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 5000
    },
    error_file: './logs/api-error.log',
    out_file: './logs/api-out.log',
    log_file: './logs/api-combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
EOF

# Create logs directory
mkdir -p /opt/plagiarism-detector/logs

# Start application
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

#### 5. Nginx Configuration
```bash
# Install Nginx
sudo apt install nginx -y

# Create Nginx configuration
sudo cat > /etc/nginx/sites-available/plagiarism-detector << EOF
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;

    # Frontend static files
    location / {
        root /opt/plagiarism-detector/frontend/dist;
        try_files \$uri \$uri/ /index.html;
        
        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API proxy
    location /api/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Health check endpoint
    location /health {
        proxy_pass http://localhost:5000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
    }
}
EOF

# Enable site
sudo ln -s /etc/nginx/sites-available/plagiarism-detector /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

#### 6. SSL Certificate (Let's Encrypt)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Option 2: Docker Deployment

#### 1. Backend Dockerfile
```dockerfile
# backend/Dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --production

# Copy source code
COPY . .

# Build application
RUN npm run build

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:5000/health || exit 1

# Start application
CMD ["npm", "start"]
```

#### 2. Frontend Dockerfile
```dockerfile
# frontend/Dockerfile
FROM node:18-alpine as builder

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci

# Copy source code and build
COPY . .
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built files
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

#### 3. Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - CORS_ORIGIN=http://localhost:3000
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    build: ./frontend
    ports:
      - "3000:80"
    depends_on:
      - backend
    restart: unless-stopped

volumes:
  logs:
```

#### 4. Deploy with Docker
```bash
# Create environment file
echo "OPENAI_API_KEY=your_api_key_here" > .env

# Build and start services
docker-compose up -d

# View logs
docker-compose logs -f

# Scale backend if needed
docker-compose up -d --scale backend=3
```

## Monitoring and Maintenance

### 1. Health Monitoring
```bash
# Check application health
curl http://localhost:5000/health

# Monitor PM2 processes
pm2 status
pm2 logs

# Monitor system resources
htop
df -h
```

### 2. Log Management
```bash
# Rotate logs with logrotate
sudo cat > /etc/logrotate.d/plagiarism-detector << EOF
/opt/plagiarism-detector/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 plagiarism-detector plagiarism-detector
    postrotate
        pm2 reloadLogs
    endscript
}
EOF
```

### 3. Backup Strategy
```bash
# Backup configuration
tar -czf backup-$(date +%Y%m%d).tar.gz \
  /opt/plagiarism-detector/.env \
  /opt/plagiarism-detector/ecosystem.config.js \
  /etc/nginx/sites-available/plagiarism-detector

# Automated backup script
cat > /opt/plagiarism-detector/backup.sh << EOF
#!/bin/bash
BACKUP_DIR="/opt/backups/plagiarism-detector"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

tar -czf $BACKUP_DIR/config-$DATE.tar.gz \
  /opt/plagiarism-detector/.env \
  /opt/plagiarism-detector/ecosystem.config.js

# Keep only last 7 days of backups
find $BACKUP_DIR -name "config-*.tar.gz" -mtime +7 -delete
EOF

chmod +x /opt/plagiarism-detector/backup.sh

# Add to crontab
echo "0 2 * * * /opt/plagiarism-detector/backup.sh" | crontab -
```

## Troubleshooting

### Common Issues

#### 1. OpenAI API Errors
```bash
# Check API key validity
curl -H "Authorization: Bearer $OPENAI_API_KEY" \
  https://api.openai.com/v1/models

# Monitor API usage
tail -f /opt/plagiarism-detector/logs/api-combined.log | grep "OpenAI"
```

#### 2. Memory Issues
```bash
# Monitor memory usage
free -h
ps aux --sort=-%mem | head

# Adjust PM2 memory limits
pm2 restart plagiarism-detector-api --max-memory-restart 512M
```

#### 3. Performance Issues
```bash
# Monitor API response times
curl -w "@curl-format.txt" -s -o /dev/null http://localhost:5000/health

# Check system load
uptime
iostat 1 5
```

### Performance Optimization

#### 1. Node.js Optimization
```bash
# Increase memory limit
export NODE_OPTIONS="--max-old-space-size=2048"

# Enable production optimizations
export NODE_ENV=production
```

#### 2. Nginx Optimization
```nginx
# Add to nginx.conf
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript;

# Enable caching
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=api_cache:10m max_size=1g;
```

## Security Considerations

### 1. API Key Security
- Store API keys in environment variables only
- Use different keys for development and production
- Regularly rotate API keys
- Monitor API usage for anomalies

### 2. Network Security
- Use HTTPS in production
- Implement proper CORS policies
- Enable rate limiting
- Use firewall rules to restrict access

### 3. Application Security
- Keep dependencies updated
- Use security headers
- Validate all inputs
- Implement proper error handling

This deployment guide provides comprehensive instructions for both development and production environments. Choose the deployment method that best fits your infrastructure and requirements.
