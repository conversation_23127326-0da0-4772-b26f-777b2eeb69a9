{"name": "discord-mcp-server", "version": "1.0.0", "description": "A Model Context Protocol server for Discord integration with secure authentication and comprehensive tools", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsx src/index.ts", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "inspector": "mcp-inspector"}, "keywords": ["mcp", "model-context-protocol", "discord", "bot", "ai", "llm"], "author": "Your Name", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "discord.js": "^14.14.1", "jsonwebtoken": "^9.0.2", "sqlite3": "^5.1.6", "dotenv": "^16.3.1", "zod": "^3.22.4", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "cors": "^2.8.5", "winston": "^3.11.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/jsonwebtoken": "^9.0.5", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/jest": "^29.5.8", "typescript": "^5.3.2", "tsx": "^4.6.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "@modelcontextprotocol/inspector": "^1.0.0"}, "engines": {"node": ">=18.0.0"}}