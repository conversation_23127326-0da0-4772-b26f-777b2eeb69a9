import { 
  Client, 
  TextChannel, 
  VoiceChannel, 
  CategoryChannel, 
  NewsChannel, 
  ThreadChannel,
  DMChannel,
  ChannelType 
} from 'discord.js';
import { z } from 'zod';
import { loggers } from '../utils/logger.js';
import { DiscordAPIError, ValidationError, DiscordChannel } from '../types/index.js';

// Input schema for get_channel_info tool
const GetChannelInfoInput = z.object({
  channel_id: z.string().min(1, "Channel ID is required")
});

type GetChannelInfoArgs = z.infer<typeof GetChannelInfoInput>;

/**
 * Get information about a Discord channel
 */
export async function getChannelInfo(client: Client, args: GetChannelInfoArgs) {
  const startTime = Date.now();
  
  try {
    loggers.info('Fetching Discord channel info', {
      channelId: args.channel_id
    });

    // Get the channel
    const channel = await client.channels.fetch(args.channel_id);
    
    if (!channel) {
      throw new ValidationError(`Channel not found: ${args.channel_id}`);
    }

    // Format channel information based on type
    const channelInfo = await formatChannelInfo(channel);
    
    const duration = Date.now() - startTime;
    
    loggers.info('Discord channel info fetched successfully', {
      channelId: args.channel_id,
      channelType: channel.type,
      duration
    });

    return {
      success: true,
      channel: channelInfo
    };

  } catch (error) {
    const duration = Date.now() - startTime;
    
    loggers.error('Failed to fetch Discord channel info', error as Error, {
      channelId: args.channel_id,
      duration
    });

    // Handle specific Discord API errors
    if (error instanceof Error) {
      if (error.message.includes('Unknown Channel')) {
        throw new ValidationError(`Channel not found or bot cannot access: ${args.channel_id}`);
      }
      
      if (error.message.includes('Missing Permissions')) {
        throw new DiscordAPIError('Bot lacks permission to view this channel');
      }
    }

    // Re-throw validation errors as-is
    if (error instanceof ValidationError) {
      throw error;
    }

    // Wrap other errors
    throw new DiscordAPIError(`Failed to fetch channel info: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Format channel information based on channel type
 */
async function formatChannelInfo(channel: any): Promise<any> {
  const baseInfo = {
    id: channel.id,
    type: channel.type,
    type_name: getChannelTypeName(channel.type),
    created_at: channel.createdAt?.toISOString()
  };

  // Add guild-specific information
  if (channel.guild) {
    Object.assign(baseInfo, {
      guild: {
        id: channel.guild.id,
        name: channel.guild.name,
        icon: channel.guild.iconURL(),
        member_count: channel.guild.memberCount
      }
    });
  }

  // Channel-type specific information
  switch (channel.type) {
    case ChannelType.GuildText:
    case ChannelType.GuildNews:
      return await formatTextChannelInfo(channel, baseInfo);
      
    case ChannelType.GuildVoice:
      return formatVoiceChannelInfo(channel, baseInfo);
      
    case ChannelType.GuildCategory:
      return formatCategoryChannelInfo(channel, baseInfo);
      
    case ChannelType.PublicThread:
    case ChannelType.PrivateThread:
    case ChannelType.NewsThread:
      return formatThreadChannelInfo(channel, baseInfo);
      
    case ChannelType.DM:
      return formatDMChannelInfo(channel, baseInfo);
      
    default:
      return baseInfo;
  }
}

/**
 * Format text channel information
 */
async function formatTextChannelInfo(channel: TextChannel | NewsChannel, baseInfo: any) {
  const textInfo = {
    ...baseInfo,
    name: channel.name,
    topic: channel.topic,
    nsfw: channel.nsfw,
    position: channel.position,
    parent: channel.parent ? {
      id: channel.parent.id,
      name: channel.parent.name
    } : null,
    rate_limit_per_user: channel.rateLimitPerUser,
    last_message_id: channel.lastMessageId,
    permissions: {
      can_send_messages: channel.permissionsFor(channel.guild.members.me!)?.has('SendMessages') ?? false,
      can_read_history: channel.permissionsFor(channel.guild.members.me!)?.has('ReadMessageHistory') ?? false,
      can_manage_messages: channel.permissionsFor(channel.guild.members.me!)?.has('ManageMessages') ?? false,
      can_embed_links: channel.permissionsFor(channel.guild.members.me!)?.has('EmbedLinks') ?? false
    }
  };

  // Get recent activity stats
  try {
    const recentMessages = await channel.messages.fetch({ limit: 10 });
    textInfo.recent_activity = {
      message_count_last_10: recentMessages.size,
      last_message_timestamp: recentMessages.first()?.createdAt?.toISOString(),
      active_users_last_10: new Set(recentMessages.map(m => m.author.id)).size
    };
  } catch (error) {
    // If we can't fetch messages, just skip recent activity
    textInfo.recent_activity = null;
  }

  return textInfo;
}

/**
 * Format voice channel information
 */
function formatVoiceChannelInfo(channel: VoiceChannel, baseInfo: any) {
  return {
    ...baseInfo,
    name: channel.name,
    position: channel.position,
    user_limit: channel.userLimit,
    bitrate: channel.bitrate,
    rtc_region: channel.rtcRegion,
    parent: channel.parent ? {
      id: channel.parent.id,
      name: channel.parent.name
    } : null,
    members: channel.members.map(member => ({
      id: member.id,
      username: member.user.username,
      nickname: member.nickname,
      muted: member.voice.mute,
      deafened: member.voice.deaf
    })),
    member_count: channel.members.size
  };
}

/**
 * Format category channel information
 */
function formatCategoryChannelInfo(channel: CategoryChannel, baseInfo: any) {
  return {
    ...baseInfo,
    name: channel.name,
    position: channel.position,
    children: channel.children.cache.map(child => ({
      id: child.id,
      name: child.name,
      type: child.type,
      type_name: getChannelTypeName(child.type)
    })),
    child_count: channel.children.cache.size
  };
}

/**
 * Format thread channel information
 */
function formatThreadChannelInfo(channel: ThreadChannel, baseInfo: any) {
  return {
    ...baseInfo,
    name: channel.name,
    parent: channel.parent ? {
      id: channel.parent.id,
      name: channel.parent.name
    } : null,
    owner: channel.ownerId ? {
      id: channel.ownerId
    } : null,
    archived: channel.archived,
    auto_archive_duration: channel.autoArchiveDuration,
    archive_timestamp: channel.archiveTimestamp?.toISOString(),
    locked: channel.locked,
    member_count: channel.memberCount,
    message_count: channel.messageCount
  };
}

/**
 * Format DM channel information
 */
function formatDMChannelInfo(channel: DMChannel, baseInfo: any) {
  return {
    ...baseInfo,
    recipient: channel.recipient ? {
      id: channel.recipient.id,
      username: channel.recipient.username,
      discriminator: channel.recipient.discriminator,
      avatar: channel.recipient.avatarURL()
    } : null,
    last_message_id: channel.lastMessageId
  };
}

/**
 * Get human-readable channel type name
 */
function getChannelTypeName(type: ChannelType): string {
  const typeNames: Record<ChannelType, string> = {
    [ChannelType.GuildText]: 'Text Channel',
    [ChannelType.DM]: 'Direct Message',
    [ChannelType.GuildVoice]: 'Voice Channel',
    [ChannelType.GroupDM]: 'Group DM',
    [ChannelType.GuildCategory]: 'Category',
    [ChannelType.GuildNews]: 'News Channel',
    [ChannelType.NewsThread]: 'News Thread',
    [ChannelType.PublicThread]: 'Public Thread',
    [ChannelType.PrivateThread]: 'Private Thread',
    [ChannelType.GuildStageVoice]: 'Stage Channel',
    [ChannelType.GuildDirectory]: 'Directory',
    [ChannelType.GuildForum]: 'Forum Channel'
  };

  return typeNames[type] || 'Unknown';
}
