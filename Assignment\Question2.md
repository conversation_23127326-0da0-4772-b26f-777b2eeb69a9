Q: 2
Plagiarism Detector - Semantic Similarity Analyzer
Requirements:
Create a web interface with dynamic input text boxes
Implement semantic similarity comparison using embeddings
Display similarity percentages between all text pairs
Identify which texts are potential clones (high similarity)
Compare different embedding models (e.g., sentence-transformers, OpenAI embeddings)
Technical Implementation:
# Core components to implement:
- Text preprocessing for multiple inputs
- Embedding generation using multiple models
- Pairwise cosine similarity calculation
- Results visualization showing similarity matrix
- Clone detection based on similarity thresholds

Deliverables:
Working web application with multiple text input boxes
Similarity matrix showing percentages between all text pairs
Clone detection highlighting (e.g., texts with >80% similarity)
Comparison report of different embedding models
Documentation explaining how embeddings detect plagiarism
Submission:
Submit GitHub URL