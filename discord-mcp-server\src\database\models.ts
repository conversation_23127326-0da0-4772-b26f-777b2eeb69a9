import sqlite3 from 'sqlite3';
import { promisify } from 'util';
import { config } from '../config/index.js';
import { loggers } from '../utils/logger.js';
import { TenantConfig, <PERSON><PERSON>ey, AuditLogEntry, Permission } from '../types/index.js';

/**
 * Database manager for SQLite operations
 */
export class DatabaseManager {
  private db: sqlite3.Database | null = null;
  private isInitialized = false;

  constructor() {
    this.initializeDatabase();
  }

  /**
   * Initialize the database connection and create tables
   */
  private async initializeDatabase(): Promise<void> {
    try {
      const dbPath = config.database.url.replace('sqlite:', '');
      
      this.db = new sqlite3.Database(dbPath, (err) => {
        if (err) {
          loggers.error('Failed to connect to database', err);
          throw err;
        }
        loggers.info('Connected to SQLite database', { path: dbPath });
      });

      await this.createTables();
      this.isInitialized = true;
      
      loggers.info('Database initialized successfully');
      
    } catch (error) {
      loggers.error('Database initialization failed', error as Error);
      throw error;
    }
  }

  /**
   * Create database tables
   */
  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not connected');

    const run = promisify(this.db.run.bind(this.db));

    // Tenants table
    await run(`
      CREATE TABLE IF NOT EXISTS tenants (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        discord_bot_token TEXT NOT NULL,
        permissions TEXT NOT NULL, -- JSON string
        quotas TEXT NOT NULL, -- JSON string
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // API Keys table
    await run(`
      CREATE TABLE IF NOT EXISTS api_keys (
        id TEXT PRIMARY KEY,
        key_hash TEXT NOT NULL UNIQUE,
        tenant_id TEXT NOT NULL,
        name TEXT NOT NULL,
        permissions TEXT NOT NULL, -- JSON string
        expires_at DATETIME,
        last_used_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (tenant_id) REFERENCES tenants (id) ON DELETE CASCADE
      )
    `);

    // Audit logs table
    await run(`
      CREATE TABLE IF NOT EXISTS audit_logs (
        id TEXT PRIMARY KEY,
        tenant_id TEXT NOT NULL,
        user_id TEXT,
        action TEXT NOT NULL,
        resource TEXT NOT NULL,
        resource_id TEXT NOT NULL,
        details TEXT NOT NULL, -- JSON string
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        ip_address TEXT,
        user_agent TEXT,
        FOREIGN KEY (tenant_id) REFERENCES tenants (id) ON DELETE CASCADE
      )
    `);

    // Rate limiting table
    await run(`
      CREATE TABLE IF NOT EXISTS rate_limits (
        id TEXT PRIMARY KEY,
        tenant_id TEXT NOT NULL,
        endpoint TEXT NOT NULL,
        requests_count INTEGER DEFAULT 0,
        window_start DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (tenant_id) REFERENCES tenants (id) ON DELETE CASCADE
      )
    `);

    // Create indexes for performance
    await run('CREATE INDEX IF NOT EXISTS idx_api_keys_tenant ON api_keys(tenant_id)');
    await run('CREATE INDEX IF NOT EXISTS idx_audit_logs_tenant ON audit_logs(tenant_id)');
    await run('CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp)');
    await run('CREATE INDEX IF NOT EXISTS idx_rate_limits_tenant ON rate_limits(tenant_id)');

    loggers.info('Database tables created successfully');
  }

  /**
   * Get database instance (with initialization check)
   */
  async getDb(): Promise<sqlite3.Database> {
    if (!this.isInitialized) {
      await this.initializeDatabase();
    }
    
    if (!this.db) {
      throw new Error('Database not initialized');
    }
    
    return this.db;
  }

  /**
   * Close database connection
   */
  async close(): Promise<void> {
    if (this.db) {
      await new Promise<void>((resolve, reject) => {
        this.db!.close((err) => {
          if (err) {
            loggers.error('Error closing database', err);
            reject(err);
          } else {
            loggers.info('Database connection closed');
            resolve();
          }
        });
      });
      this.db = null;
      this.isInitialized = false;
    }
  }

  /**
   * Create a new tenant
   */
  async createTenant(tenant: Omit<TenantConfig, 'createdAt' | 'updatedAt'>): Promise<TenantConfig> {
    const db = await this.getDb();
    const run = promisify(db.run.bind(db));

    const now = new Date().toISOString();
    
    await run(`
      INSERT INTO tenants (id, name, discord_bot_token, permissions, quotas, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      tenant.id,
      tenant.name,
      tenant.discordBotToken,
      JSON.stringify(tenant.permissions),
      JSON.stringify(tenant.quotas),
      now,
      now
    ]);

    loggers.info('Tenant created', { tenantId: tenant.id, name: tenant.name });

    return {
      ...tenant,
      createdAt: new Date(now),
      updatedAt: new Date(now)
    };
  }

  /**
   * Get tenant by ID
   */
  async getTenant(tenantId: string): Promise<TenantConfig | null> {
    const db = await this.getDb();
    const get = promisify(db.get.bind(db));

    const row = await get('SELECT * FROM tenants WHERE id = ?', [tenantId]) as any;
    
    if (!row) return null;

    return {
      id: row.id,
      name: row.name,
      discordBotToken: row.discord_bot_token,
      permissions: JSON.parse(row.permissions),
      quotas: JSON.parse(row.quotas),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }

  /**
   * Update tenant
   */
  async updateTenant(tenantId: string, updates: Partial<TenantConfig>): Promise<void> {
    const db = await this.getDb();
    const run = promisify(db.run.bind(db));

    const setClause = [];
    const values = [];

    if (updates.name) {
      setClause.push('name = ?');
      values.push(updates.name);
    }

    if (updates.discordBotToken) {
      setClause.push('discord_bot_token = ?');
      values.push(updates.discordBotToken);
    }

    if (updates.permissions) {
      setClause.push('permissions = ?');
      values.push(JSON.stringify(updates.permissions));
    }

    if (updates.quotas) {
      setClause.push('quotas = ?');
      values.push(JSON.stringify(updates.quotas));
    }

    setClause.push('updated_at = ?');
    values.push(new Date().toISOString());

    values.push(tenantId);

    await run(`UPDATE tenants SET ${setClause.join(', ')} WHERE id = ?`, values);
    
    loggers.info('Tenant updated', { tenantId, updates: Object.keys(updates) });
  }

  /**
   * Delete tenant
   */
  async deleteTenant(tenantId: string): Promise<void> {
    const db = await this.getDb();
    const run = promisify(db.run.bind(db));

    await run('DELETE FROM tenants WHERE id = ?', [tenantId]);
    
    loggers.info('Tenant deleted', { tenantId });
  }

  /**
   * Create API key
   */
  async createApiKey(apiKey: Omit<ApiKey, 'createdAt'>): Promise<ApiKey> {
    const db = await this.getDb();
    const run = promisify(db.run.bind(db));

    const now = new Date().toISOString();

    await run(`
      INSERT INTO api_keys (id, key_hash, tenant_id, name, permissions, expires_at, last_used_at, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      apiKey.id,
      apiKey.key, // This should be hashed in production
      apiKey.tenantId,
      apiKey.name,
      JSON.stringify(apiKey.permissions),
      apiKey.expiresAt?.toISOString(),
      apiKey.lastUsedAt?.toISOString(),
      now
    ]);

    loggers.info('API key created', { keyId: apiKey.id, tenantId: apiKey.tenantId });

    return {
      ...apiKey,
      createdAt: new Date(now)
    };
  }

  /**
   * Get API key by key hash
   */
  async getApiKeyByHash(keyHash: string): Promise<ApiKey | null> {
    const db = await this.getDb();
    const get = promisify(db.get.bind(db));

    const row = await get('SELECT * FROM api_keys WHERE key_hash = ?', [keyHash]) as any;
    
    if (!row) return null;

    return {
      id: row.id,
      key: row.key_hash,
      tenantId: row.tenant_id,
      name: row.name,
      permissions: JSON.parse(row.permissions),
      expiresAt: row.expires_at ? new Date(row.expires_at) : undefined,
      lastUsedAt: row.last_used_at ? new Date(row.last_used_at) : undefined,
      createdAt: new Date(row.created_at)
    };
  }

  /**
   * Update API key last used timestamp
   */
  async updateApiKeyLastUsed(keyId: string): Promise<void> {
    const db = await this.getDb();
    const run = promisify(db.run.bind(db));

    await run('UPDATE api_keys SET last_used_at = ? WHERE id = ?', [
      new Date().toISOString(),
      keyId
    ]);
  }

  /**
   * Create audit log entry
   */
  async createAuditLog(entry: Omit<AuditLogEntry, 'timestamp'>): Promise<void> {
    const db = await this.getDb();
    const run = promisify(db.run.bind(db));

    await run(`
      INSERT INTO audit_logs (id, tenant_id, user_id, action, resource, resource_id, details, timestamp, ip_address, user_agent)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      entry.id,
      entry.tenantId,
      entry.userId,
      entry.action,
      entry.resource,
      entry.resourceId,
      JSON.stringify(entry.details),
      new Date().toISOString(),
      entry.ipAddress,
      entry.userAgent
    ]);
  }

  /**
   * Get audit logs for a tenant
   */
  async getAuditLogs(tenantId: string, limit: number = 100, offset: number = 0): Promise<AuditLogEntry[]> {
    const db = await this.getDb();
    const all = promisify(db.all.bind(db));

    const rows = await all(`
      SELECT * FROM audit_logs 
      WHERE tenant_id = ? 
      ORDER BY timestamp DESC 
      LIMIT ? OFFSET ?
    `, [tenantId, limit, offset]) as any[];

    return rows.map(row => ({
      id: row.id,
      tenantId: row.tenant_id,
      userId: row.user_id,
      action: row.action,
      resource: row.resource,
      resourceId: row.resource_id,
      details: JSON.parse(row.details),
      timestamp: new Date(row.timestamp),
      ipAddress: row.ip_address,
      userAgent: row.user_agent
    }));
  }
}

// Export singleton instance
export const db = new DatabaseManager();
