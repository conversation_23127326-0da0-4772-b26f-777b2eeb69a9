{"name": "plagiarism-detector-backend", "version": "1.0.0", "description": "Backend API for Plagiarism Detection using Semantic Similarity", "main": "dist/index.js", "type": "module", "scripts": {"dev": "tsx src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "zod": "^3.22.4", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "openai": "^4.20.1", "@xenova/transformers": "^2.6.0", "sentence-transformers": "^0.1.0", "ml-matrix": "^6.10.4", "natural": "^6.7.0", "compromise": "^14.10.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/jest": "^29.5.8", "@types/natural": "^5.1.3", "typescript": "^5.3.2", "tsx": "^4.6.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "supertest": "^6.3.3", "@types/supertest": "^2.0.16"}, "engines": {"node": ">=18.0.0"}}