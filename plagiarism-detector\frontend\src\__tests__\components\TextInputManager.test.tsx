import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import TextInputManager from '../../components/TextInputManager';

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    textarea: ({ children, ...props }: any) => <textarea {...props}>{children}</textarea>,
  },
  AnimatePresence: ({ children }: any) => children,
}));

// Mock lucide-react icons
jest.mock('lucide-react', () => ({
  Plus: () => <div data-testid="plus-icon">+</div>,
  Trash2: () => <div data-testid="trash-icon">🗑</div>,
  FileText: () => <div data-testid="file-icon">📄</div>,
  AlertCircle: () => <div data-testid="alert-icon">⚠</div>,
  CheckCircle: () => <div data-testid="check-icon">✓</div>,
}));

describe('TextInputManager', () => {
  const mockOnTextsChange = jest.fn();

  beforeEach(() => {
    mockOnTextsChange.mockClear();
  });

  it('renders with initial state', () => {
    render(<TextInputManager onTextsChange={mockOnTextsChange} />);
    
    expect(screen.getByText('Text Documents')).toBeInTheDocument();
    expect(screen.getByText('Add Text')).toBeInTheDocument();
    expect(screen.getByText('0 / 10 texts')).toBeInTheDocument();
  });

  it('adds a new text input when Add Text button is clicked', async () => {
    const user = userEvent.setup();
    render(<TextInputManager onTextsChange={mockOnTextsChange} />);
    
    const addButton = screen.getByText('Add Text');
    await user.click(addButton);
    
    expect(screen.getByText('1 / 10 texts')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter your text here...')).toBeInTheDocument();
  });

  it('removes text input when delete button is clicked', async () => {
    const user = userEvent.setup();
    render(<TextInputManager onTextsChange={mockOnTextsChange} />);
    
    // Add a text input first
    const addButton = screen.getByText('Add Text');
    await user.click(addButton);
    
    expect(screen.getByText('1 / 10 texts')).toBeInTheDocument();
    
    // Remove the text input
    const deleteButton = screen.getByTestId('trash-icon').closest('button');
    await user.click(deleteButton!);
    
    expect(screen.getByText('0 / 10 texts')).toBeInTheDocument();
  });

  it('updates text content when user types', async () => {
    const user = userEvent.setup();
    render(<TextInputManager onTextsChange={mockOnTextsChange} />);
    
    // Add a text input
    const addButton = screen.getByText('Add Text');
    await user.click(addButton);
    
    const textarea = screen.getByPlaceholderText('Enter your text here...');
    await user.type(textarea, 'This is a test document');
    
    await waitFor(() => {
      expect(mockOnTextsChange).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            content: 'This is a test document',
          })
        ])
      );
    });
  });

  it('updates label when user changes it', async () => {
    const user = userEvent.setup();
    render(<TextInputManager onTextsChange={mockOnTextsChange} />);
    
    // Add a text input
    const addButton = screen.getByText('Add Text');
    await user.click(addButton);
    
    const labelInput = screen.getByDisplayValue('Document 1');
    await user.clear(labelInput);
    await user.type(labelInput, 'Custom Label');
    
    await waitFor(() => {
      expect(mockOnTextsChange).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            label: 'Custom Label',
          })
        ])
      );
    });
  });

  it('shows validation error for text that is too short', async () => {
    const user = userEvent.setup();
    render(<TextInputManager onTextsChange={mockOnTextsChange} />);
    
    // Add a text input
    const addButton = screen.getByText('Add Text');
    await user.click(addButton);
    
    const textarea = screen.getByPlaceholderText('Enter your text here...');
    await user.type(textarea, 'Hi'); // Too short
    
    await waitFor(() => {
      expect(screen.getByText('Text must be at least 10 characters long')).toBeInTheDocument();
    });
  });

  it('shows validation error for text that is too long', async () => {
    const user = userEvent.setup();
    render(<TextInputManager onTextsChange={mockOnTextsChange} />);
    
    // Add a text input
    const addButton = screen.getByText('Add Text');
    await user.click(addButton);
    
    const textarea = screen.getByPlaceholderText('Enter your text here...');
    const longText = 'x'.repeat(10001); // Too long
    await user.type(textarea, longText);
    
    await waitFor(() => {
      expect(screen.getByText('Text must be less than 10000 characters long')).toBeInTheDocument();
    });
  });

  it('respects maximum text limit', async () => {
    const user = userEvent.setup();
    render(<TextInputManager onTextsChange={mockOnTextsChange} maxTexts={2} />);
    
    const addButton = screen.getByText('Add Text');
    
    // Add first text
    await user.click(addButton);
    expect(screen.getByText('1 / 2 texts')).toBeInTheDocument();
    
    // Add second text
    await user.click(addButton);
    expect(screen.getByText('2 / 2 texts')).toBeInTheDocument();
    
    // Button should be disabled now
    expect(addButton).toBeDisabled();
  });

  it('respects minimum text requirement', () => {
    render(<TextInputManager onTextsChange={mockOnTextsChange} minTexts={3} />);
    
    expect(screen.getByText('0 / 10 texts')).toBeInTheDocument();
    expect(screen.getByText('Minimum 3 texts required')).toBeInTheDocument();
  });

  it('shows character count for each text input', async () => {
    const user = userEvent.setup();
    render(<TextInputManager onTextsChange={mockOnTextsChange} />);
    
    // Add a text input
    const addButton = screen.getByText('Add Text');
    await user.click(addButton);
    
    const textarea = screen.getByPlaceholderText('Enter your text here...');
    await user.type(textarea, 'Hello world');
    
    await waitFor(() => {
      expect(screen.getByText('11 characters')).toBeInTheDocument();
    });
  });

  it('shows word count for each text input', async () => {
    const user = userEvent.setup();
    render(<TextInputManager onTextsChange={mockOnTextsChange} />);
    
    // Add a text input
    const addButton = screen.getByText('Add Text');
    await user.click(addButton);
    
    const textarea = screen.getByPlaceholderText('Enter your text here...');
    await user.type(textarea, 'Hello world test');
    
    await waitFor(() => {
      expect(screen.getByText('3 words')).toBeInTheDocument();
    });
  });

  it('shows summary statistics', async () => {
    const user = userEvent.setup();
    render(<TextInputManager onTextsChange={mockOnTextsChange} />);
    
    // Add two text inputs
    const addButton = screen.getByText('Add Text');
    await user.click(addButton);
    await user.click(addButton);
    
    const textareas = screen.getAllByPlaceholderText('Enter your text here...');
    await user.type(textareas[0], 'First document content');
    await user.type(textareas[1], 'Second document content');
    
    await waitFor(() => {
      expect(screen.getByText(/Total: \d+ characters/)).toBeInTheDocument();
      expect(screen.getByText(/Average: \d+ characters/)).toBeInTheDocument();
    });
  });

  it('handles paste events correctly', async () => {
    const user = userEvent.setup();
    render(<TextInputManager onTextsChange={mockOnTextsChange} />);
    
    // Add a text input
    const addButton = screen.getByText('Add Text');
    await user.click(addButton);
    
    const textarea = screen.getByPlaceholderText('Enter your text here...');
    
    // Simulate paste event
    const pasteText = 'This is pasted content';
    await user.click(textarea);
    await user.paste(pasteText);
    
    await waitFor(() => {
      expect(mockOnTextsChange).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            content: pasteText,
          })
        ])
      );
    });
  });

  it('maintains focus management correctly', async () => {
    const user = userEvent.setup();
    render(<TextInputManager onTextsChange={mockOnTextsChange} />);
    
    // Add a text input
    const addButton = screen.getByText('Add Text');
    await user.click(addButton);
    
    const textarea = screen.getByPlaceholderText('Enter your text here...');
    
    // Focus should be on the textarea after adding
    expect(textarea).toHaveFocus();
  });

  it('shows appropriate status indicators', async () => {
    const user = userEvent.setup();
    render(<TextInputManager onTextsChange={mockOnTextsChange} />);
    
    // Add a text input with valid content
    const addButton = screen.getByText('Add Text');
    await user.click(addButton);
    
    const textarea = screen.getByPlaceholderText('Enter your text here...');
    await user.type(textarea, 'This is a valid document with sufficient length');
    
    await waitFor(() => {
      expect(screen.getByTestId('check-icon')).toBeInTheDocument();
    });
  });

  it('handles keyboard shortcuts', async () => {
    const user = userEvent.setup();
    render(<TextInputManager onTextsChange={mockOnTextsChange} />);
    
    // Add a text input
    const addButton = screen.getByText('Add Text');
    await user.click(addButton);
    
    const textarea = screen.getByPlaceholderText('Enter your text here...');
    await user.click(textarea);
    
    // Test Ctrl+A (select all)
    await user.keyboard('{Control>}a{/Control}');
    
    // The textarea should still be focused
    expect(textarea).toHaveFocus();
  });
});
