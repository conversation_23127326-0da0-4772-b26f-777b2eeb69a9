@echo off
REM Plagiarism Detector Deployment Script for Windows
REM This script automates the deployment process for both development and production environments

setlocal enabledelayedexpansion

REM Configuration
set PROJECT_NAME=plagiarism-detector
set BACKEND_DIR=plagiarism-detector\backend
set FRONTEND_DIR=plagiarism-detector\frontend
set LOG_FILE=deployment.log

REM Colors (limited support in Windows)
set RED=[91m
set GREEN=[92m
set YELLOW=[93m
set BLUE=[94m
set NC=[0m

REM Functions
:log
echo [%date% %time%] %~1 >> %LOG_FILE%
echo %BLUE%[%date% %time%]%NC% %~1
goto :eof

:success
echo [SUCCESS] %~1 >> %LOG_FILE%
echo %GREEN%[SUCCESS]%NC% %~1
goto :eof

:warning
echo [WARNING] %~1 >> %LOG_FILE%
echo %YELLOW%[WARNING]%NC% %~1
goto :eof

:error
echo [ERROR] %~1 >> %LOG_FILE%
echo %RED%[ERROR]%NC% %~1
exit /b 1

:check_prerequisites
call :log "Checking prerequisites..."

REM Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    call :error "Node.js is not installed. Please install Node.js 18 or higher."
    goto :eof
)

REM Check npm
npm --version >nul 2>&1
if errorlevel 1 (
    call :error "npm is not installed."
    goto :eof
)

call :success "Prerequisites check passed"
goto :eof

:install_dependencies
call :log "Installing dependencies..."

REM Backend dependencies
call :log "Installing backend dependencies..."
cd %BACKEND_DIR%
call npm ci
if errorlevel 1 (
    call :error "Failed to install backend dependencies"
    goto :eof
)
cd ..\..

REM Frontend dependencies
call :log "Installing frontend dependencies..."
cd %FRONTEND_DIR%
call npm ci
if errorlevel 1 (
    call :error "Failed to install frontend dependencies"
    goto :eof
)
cd ..\..

call :success "Dependencies installed successfully"
goto :eof

:build_applications
call :log "Building applications..."

REM Build backend
call :log "Building backend..."
cd %BACKEND_DIR%
call npm run build
if errorlevel 1 (
    call :error "Failed to build backend"
    goto :eof
)
cd ..\..

REM Build frontend
call :log "Building frontend..."
cd %FRONTEND_DIR%
call npm run build
if errorlevel 1 (
    call :error "Failed to build frontend"
    goto :eof
)
cd ..\..

call :success "Applications built successfully"
goto :eof

:run_tests
call :log "Running tests..."

REM Backend tests
call :log "Running backend tests..."
cd %BACKEND_DIR%
call npm test
if errorlevel 1 (
    call :warning "Some backend tests failed"
)
cd ..\..

REM Frontend tests
call :log "Running frontend tests..."
cd %FRONTEND_DIR%
call npm run test:run
if errorlevel 1 (
    call :warning "Some frontend tests failed"
)
cd ..\..

call :success "Tests completed"
goto :eof

:setup_environment
call :log "Setting up environment..."

REM Check for environment files
if not exist "%BACKEND_DIR%\.env" (
    call :warning "Backend .env file not found. Please create one based on .env.example"
)

if not exist "%FRONTEND_DIR%\.env.production" (
    call :warning "Frontend .env.production file not found. Please create one based on .env.example"
)

REM Create logs directory
if not exist "%BACKEND_DIR%\logs" (
    mkdir "%BACKEND_DIR%\logs"
)

call :success "Environment setup completed"
goto :eof

:deploy_pm2
call :log "Deploying with PM2..."

REM Check if PM2 is installed
pm2 --version >nul 2>&1
if errorlevel 1 (
    call :log "Installing PM2..."
    call npm install -g pm2
    if errorlevel 1 (
        call :error "Failed to install PM2"
        goto :eof
    )
)

REM Start backend with PM2
cd %BACKEND_DIR%
call pm2 start ecosystem.config.js --env production
if errorlevel 1 (
    call :error "Failed to start backend with PM2"
    goto :eof
)
cd ..\..

REM Start frontend with serve
call :log "Starting frontend with serve..."
where serve >nul 2>&1
if errorlevel 1 (
    call npm install -g serve
)

cd %FRONTEND_DIR%
start /b pm2 start "serve -s dist -l 3000" --name "%PROJECT_NAME%-frontend"
cd ..\..

call :success "PM2 deployment completed successfully"
goto :eof

:health_check
call :log "Performing health check..."

REM Wait a moment for services to start
timeout /t 10 /nobreak >nul

REM Check backend (using PowerShell for HTTP request)
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:5000/health' -UseBasicParsing | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if errorlevel 1 (
    call :warning "Backend health check failed - service may still be starting"
) else (
    call :success "Backend health check passed"
)

REM Check frontend
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:3000' -UseBasicParsing | Out-Null; exit 0 } catch { exit 1 }" >nul 2>&1
if errorlevel 1 (
    call :warning "Frontend health check failed - service may still be starting"
) else (
    call :success "Frontend health check passed"
)

goto :eof

:cleanup
call :log "Cleaning up..."

REM Remove temporary files
del /q *.tmp 2>nul
del /q *.log 2>nul | findstr /v %LOG_FILE%

call :success "Cleanup completed"
goto :eof

:deploy_main
set deployment_type=%1
if "%deployment_type%"=="" set deployment_type=pm2

call :log "Starting deployment process..."
call :log "Deployment type: %deployment_type%"

call :check_prerequisites
if errorlevel 1 goto :eof

call :setup_environment
if errorlevel 1 goto :eof

call :install_dependencies
if errorlevel 1 goto :eof

call :build_applications
if errorlevel 1 goto :eof

call :run_tests

if "%deployment_type%"=="pm2" (
    call :deploy_pm2
    if errorlevel 1 goto :eof
) else (
    call :error "Unknown deployment type: %deployment_type%. Use 'pm2'"
    goto :eof
)

call :health_check
call :cleanup

call :success "Deployment completed successfully!"
call :log "Application is now running:"
call :log "  - Backend: http://localhost:5000"
call :log "  - Frontend: http://localhost:3000"
call :log "  - Health Check: http://localhost:5000/health"

goto :eof

:usage
echo Usage: %0 [deployment_type]
echo   deployment_type: 'pm2' (default: pm2)
echo.
echo Examples:
echo   %0              # Deploy with PM2
echo   %0 pm2          # Deploy with PM2
goto :eof

REM Main script execution
if "%1"=="--help" goto :usage
if "%1"=="-h" goto :usage

call :deploy_main %1
