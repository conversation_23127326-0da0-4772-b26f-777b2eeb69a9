import { config } from 'dotenv';

// Load test environment variables
config({ path: '.env.test' });

// Set test environment
process.env.NODE_ENV = 'test';

// Mock OpenAI API for tests
process.env.OPENAI_API_KEY = 'test-api-key';

// Global test timeout
jest.setTimeout(30000);

// Mock console methods to reduce noise in tests
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeAll(() => {
  console.error = jest.fn();
  console.warn = jest.fn();
});

afterAll(() => {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
});

// Global test utilities
(global as any).testUtils = {
  createMockEmbedding: (textId: string, dimensions: number = 4) => ({
    textId,
    model: 'test-model',
    vector: Array.from({ length: dimensions }, () => Math.random()),
    dimensions,
    metadata: {
      processingTime: 100,
      timestamp: new Date(),
    },
  }),

  createMockProcessedText: (textId: string, content: string) => ({
    textId,
    originalText: content,
    processedText: content.toLowerCase(),
    wordCount: content.split(' ').length,
    characterCount: content.length,
    language: 'en',
    processingTime: 50,
    metadata: {
      preprocessing: {
        removeStopWords: false,
        stemming: false,
        lemmatization: true,
        removeNumbers: false,
        removePunctuation: false,
        toLowerCase: true,
        removeExtraWhitespace: true,
        minWordLength: 1,
      },
      statistics: {
        originalLength: content.length,
        processedLength: content.toLowerCase().length,
        reductionPercentage: 0,
      },
    },
  }),

  delay: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
};
