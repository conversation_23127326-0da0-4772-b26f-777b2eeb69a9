import { Client, TextChannel, DMChannel, NewsChannel, ThreadChannel, Message } from 'discord.js';
import { z } from 'zod';
import { loggers } from '../utils/logger.js';
import { DiscordAPIError, ValidationError, DiscordMessage } from '../types/index.js';
import { performanceConfig } from '../config/index.js';

// Input schema for search_messages tool
const SearchMessagesInput = z.object({
  query: z.string().min(1, "Search query is required"),
  channel_id: z.string().optional(),
  author_id: z.string().optional(),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  limit: z.number().min(1).max(50).default(25)
});

type SearchMessagesArgs = z.infer<typeof SearchMessagesInput>;

/**
 * Search for messages in Discord channels with filters
 */
export async function searchMessages(client: Client, args: SearchMessagesArgs) {
  const startTime = Date.now();
  
  try {
    loggers.info('Searching Discord messages', {
      query: args.query,
      channelId: args.channel_id,
      authorId: args.author_id,
      dateFrom: args.date_from,
      dateTo: args.date_to,
      limit: args.limit
    });

    // Parse date filters
    const dateFrom = args.date_from ? new Date(args.date_from) : null;
    const dateTo = args.date_to ? new Date(args.date_to) : null;

    // Validate dates
    if (dateFrom && isNaN(dateFrom.getTime())) {
      throw new ValidationError('Invalid date_from format. Use ISO string format.');
    }
    
    if (dateTo && isNaN(dateTo.getTime())) {
      throw new ValidationError('Invalid date_to format. Use ISO string format.');
    }

    let searchResults: DiscordMessage[] = [];
    let searchedChannels: string[] = [];

    if (args.channel_id) {
      // Search in specific channel
      const results = await searchInChannel(
        client, 
        args.channel_id, 
        args.query, 
        args.author_id, 
        dateFrom, 
        dateTo, 
        args.limit
      );
      searchResults = results.messages;
      searchedChannels = [args.channel_id];
    } else {
      // Search across all accessible channels
      const results = await searchAcrossChannels(
        client, 
        args.query, 
        args.author_id, 
        dateFrom, 
        dateTo, 
        args.limit
      );
      searchResults = results.messages;
      searchedChannels = results.channels;
    }

    // Sort results by relevance and timestamp
    searchResults = rankSearchResults(searchResults, args.query);

    const duration = Date.now() - startTime;
    
    loggers.info('Discord message search completed', {
      query: args.query,
      resultCount: searchResults.length,
      channelsSearched: searchedChannels.length,
      duration
    });

    return {
      success: true,
      query: args.query,
      filters: {
        channel_id: args.channel_id,
        author_id: args.author_id,
        date_from: args.date_from,
        date_to: args.date_to
      },
      results: {
        messages: searchResults,
        total_found: searchResults.length,
        channels_searched: searchedChannels,
        search_duration_ms: duration
      },
      pagination: {
        limit: args.limit,
        has_more: searchResults.length === args.limit
      }
    };

  } catch (error) {
    const duration = Date.now() - startTime;
    
    loggers.error('Failed to search Discord messages', error as Error, {
      query: args.query,
      duration
    });

    // Re-throw validation errors as-is
    if (error instanceof ValidationError) {
      throw error;
    }

    // Wrap other errors
    throw new DiscordAPIError(`Failed to search messages: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Search messages in a specific channel
 */
async function searchInChannel(
  client: Client,
  channelId: string,
  query: string,
  authorId?: string,
  dateFrom?: Date | null,
  dateTo?: Date | null,
  limit: number = 25
): Promise<{ messages: DiscordMessage[] }> {
  
  const channel = await client.channels.fetch(channelId);
  
  if (!channel) {
    throw new ValidationError(`Channel not found: ${channelId}`);
  }

  if (!isTextBasedChannel(channel)) {
    throw new ValidationError(`Channel ${channelId} is not a text-based channel`);
  }

  const messages: DiscordMessage[] = [];
  let lastMessageId: string | undefined;
  const maxFetch = Math.min(limit * 10, 500); // Fetch more to filter, but cap it

  while (messages.length < limit) {
    const fetchOptions: any = { 
      limit: Math.min(100, maxFetch - messages.length * 10)
    };
    
    if (lastMessageId) {
      fetchOptions.before = lastMessageId;
    }

    const fetchedMessages = await channel.messages.fetch(fetchOptions);
    
    if (fetchedMessages.size === 0) break;

    for (const [, message] of fetchedMessages) {
      if (messages.length >= limit) break;

      if (matchesSearchCriteria(message, query, authorId, dateFrom, dateTo)) {
        messages.push(formatDiscordMessage(message));
      }
    }

    lastMessageId = fetchedMessages.last()?.id;
    
    // Prevent infinite loops
    if (fetchedMessages.size < 100) break;
  }

  return { messages };
}

/**
 * Search messages across all accessible channels
 */
async function searchAcrossChannels(
  client: Client,
  query: string,
  authorId?: string,
  dateFrom?: Date | null,
  dateTo?: Date | null,
  limit: number = 25
): Promise<{ messages: DiscordMessage[]; channels: string[] }> {
  
  const messages: DiscordMessage[] = [];
  const searchedChannels: string[] = [];
  
  // Get all guilds the bot is in
  const guilds = client.guilds.cache;
  
  for (const [, guild] of guilds) {
    if (messages.length >= limit) break;
    
    // Get text channels in this guild
    const textChannels = guild.channels.cache.filter(ch => 
      isTextBasedChannel(ch) && 
      ch.permissionsFor(guild.members.me!)?.has('ReadMessageHistory')
    );
    
    for (const [, channel] of textChannels) {
      if (messages.length >= limit) break;
      
      try {
        const channelResults = await searchInChannel(
          client,
          channel.id,
          query,
          authorId,
          dateFrom,
          dateTo,
          Math.min(5, limit - messages.length) // Limit per channel
        );
        
        messages.push(...channelResults.messages);
        searchedChannels.push(channel.id);
        
      } catch (error) {
        // Skip channels we can't access
        loggers.debug('Skipped channel in search', { 
          channelId: channel.id, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }
    }
  }

  return { messages, channels: searchedChannels };
}

/**
 * Check if a message matches search criteria
 */
function matchesSearchCriteria(
  message: Message,
  query: string,
  authorId?: string,
  dateFrom?: Date | null,
  dateTo?: Date | null
): boolean {
  // Text content search (case-insensitive)
  const contentMatch = message.content.toLowerCase().includes(query.toLowerCase());
  
  // Author filter
  if (authorId && message.author.id !== authorId) {
    return false;
  }
  
  // Date range filter
  if (dateFrom && message.createdAt < dateFrom) {
    return false;
  }
  
  if (dateTo && message.createdAt > dateTo) {
    return false;
  }
  
  return contentMatch;
}

/**
 * Rank search results by relevance
 */
function rankSearchResults(messages: DiscordMessage[], query: string): DiscordMessage[] {
  const queryLower = query.toLowerCase();
  
  return messages.sort((a, b) => {
    // Calculate relevance scores
    const scoreA = calculateRelevanceScore(a, queryLower);
    const scoreB = calculateRelevanceScore(b, queryLower);
    
    if (scoreA !== scoreB) {
      return scoreB - scoreA; // Higher score first
    }
    
    // If same relevance, sort by timestamp (newer first)
    return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
  });
}

/**
 * Calculate relevance score for a message
 */
function calculateRelevanceScore(message: DiscordMessage, queryLower: string): number {
  const content = message.content.toLowerCase();
  let score = 0;
  
  // Exact phrase match gets highest score
  if (content.includes(queryLower)) {
    score += 10;
  }
  
  // Word matches
  const queryWords = queryLower.split(/\s+/);
  const contentWords = content.split(/\s+/);
  
  for (const queryWord of queryWords) {
    for (const contentWord of contentWords) {
      if (contentWord === queryWord) {
        score += 5; // Exact word match
      } else if (contentWord.includes(queryWord)) {
        score += 2; // Partial word match
      }
    }
  }
  
  // Boost score for shorter messages (more focused)
  if (content.length < 100) {
    score += 1;
  }
  
  return score;
}

/**
 * Format a Discord message to our standard format
 */
function formatDiscordMessage(message: Message): DiscordMessage {
  return {
    id: message.id,
    content: message.content,
    author: {
      id: message.author.id,
      username: message.author.username,
      discriminator: message.author.discriminator
    },
    channel_id: message.channelId,
    guild_id: message.guildId || undefined,
    timestamp: message.createdAt.toISOString(),
    edited_timestamp: message.editedAt?.toISOString(),
    embeds: message.embeds.map(embed => ({
      title: embed.title || undefined,
      description: embed.description || undefined,
      color: embed.color || undefined,
      fields: embed.fields.map(field => ({
        name: field.name,
        value: field.value,
        inline: field.inline
      })),
      footer: embed.footer ? {
        text: embed.footer.text,
        icon_url: embed.footer.iconURL || undefined
      } : undefined,
      timestamp: embed.timestamp || undefined
    }))
  };
}

/**
 * Type guard to check if channel supports reading messages
 */
function isTextBasedChannel(channel: any): channel is TextChannel | DMChannel | NewsChannel | ThreadChannel {
  return channel && typeof channel.messages?.fetch === 'function';
}
