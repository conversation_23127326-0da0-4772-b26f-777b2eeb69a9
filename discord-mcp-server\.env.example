# Discord Configuration
DISCORD_BOT_TOKEN=your_discord_bot_token_here
DISCORD_APPLICATION_ID=your_discord_application_id_here

# MCP Server Configuration
MCP_SERVER_NAME=discord-mcp-server
MCP_SERVER_VERSION=1.0.0
MCP_SERVER_PORT=3000

# Authentication & Security
JWT_SECRET=your_jwt_secret_key_here
API_KEYS=api_key_1:tenant_1,api_key_2:tenant_2
ENCRYPTION_KEY=your_32_character_encryption_key

# Database Configuration
DATABASE_URL=sqlite:./data/discord_mcp.db
DATABASE_MAX_CONNECTIONS=10

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/discord-mcp-server.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# MCP Inspector (Development)
MCP_INSPECTOR_PORT=3001
MCP_INSPECTOR_ENABLED=true

# Discord API Configuration
DISCORD_API_VERSION=10
DISCORD_INTENTS=GUILDS,GUILD_MESSAGES,MESSAGE_CONTENT,GUILD_MEMBERS

# Security Headers
CORS_ORIGIN=http://localhost:3001
CORS_CREDENTIALS=true

# Audit Logging
AUDIT_LOG_ENABLED=true
AUDIT_LOG_RETENTION_DAYS=90

# Performance
CACHE_TTL_SECONDS=300
MAX_MESSAGE_HISTORY=100
MAX_SEARCH_RESULTS=50
