# Deployment Guide - Plagiarism Detector

## Overview
This guide provides comprehensive instructions for deploying the Plagiarism Detector application in production environments.

## Prerequisites

### System Requirements
- **Node.js**: Version 18.0 or higher
- **npm**: Version 8.0 or higher
- **Memory**: Minimum 2GB RAM (4GB recommended)
- **Storage**: Minimum 1GB free space
- **Network**: Internet access for API calls and model downloads

### API Keys Required
- **OpenAI API Key**: For text-embedding-ada-002 model
- **Optional**: Hugging Face API token for enhanced model access

## Environment Setup

### 1. Clone and Install Dependencies

```bash
# Clone the repository
git clone <repository-url>
cd plagiarism-detector

# Install backend dependencies
cd backend
npm install --production

# Install frontend dependencies
cd ../frontend
npm install --production
```

### 2. Environment Configuration

Create environment files for both backend and frontend:

**Backend (.env)**:
```env
# Server Configuration
NODE_ENV=production
PORT=5000
HOST=0.0.0.0

# API Keys
OPENAI_API_KEY=your_openai_api_key_here
HUGGINGFACE_API_TOKEN=your_hf_token_here

# CORS Configuration
CORS_ORIGIN=https://your-frontend-domain.com
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Security
HELMET_CSP_ENABLED=true
TRUST_PROXY=true

# Logging
LOG_LEVEL=info
LOG_FORMAT=combined

# Performance
MAX_TEXT_LENGTH=50000
MAX_TEXTS_PER_REQUEST=20
EMBEDDING_TIMEOUT=30000
```

**Frontend (.env.production)**:
```env
VITE_API_BASE_URL=https://your-backend-domain.com
VITE_APP_TITLE=Plagiarism Detector
VITE_MAX_TEXT_LENGTH=50000
VITE_MAX_TEXTS=20
VITE_ENABLE_ANALYTICS=true
```

## Production Build

### 1. Build Backend

```bash
cd backend
npm run build
```

This creates a `dist/` directory with compiled TypeScript files.

### 2. Build Frontend

```bash
cd frontend
npm run build
```

This creates a `dist/` directory with optimized static files.

## Deployment Options

### Option 1: Traditional Server Deployment

#### Backend Deployment
```bash
# Navigate to backend directory
cd backend

# Start production server
npm start

# Or use PM2 for process management
npm install -g pm2
pm2 start ecosystem.config.js
```

#### Frontend Deployment
Serve the built frontend files using a web server:

```bash
# Using nginx (recommended)
sudo cp -r frontend/dist/* /var/www/html/

# Or using serve
npm install -g serve
serve -s frontend/dist -l 3000
```

### Option 2: Docker Deployment

#### Backend Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY dist ./dist
COPY public ./public

EXPOSE 5000

CMD ["npm", "start"]
```

#### Frontend Dockerfile
```dockerfile
FROM nginx:alpine

COPY dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

#### Docker Compose
```yaml
version: '3.8'

services:
  backend:
    build: ./backend
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    restart: unless-stopped

  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    restart: unless-stopped
```

### Option 3: Cloud Platform Deployment

#### Vercel (Frontend)
```json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "dist"
      }
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/index.html"
    }
  ]
}
```

#### Railway/Render (Backend)
```json
{
  "build": {
    "commands": [
      "npm ci",
      "npm run build"
    ]
  },
  "start": {
    "command": "npm start"
  }
}
```

## Performance Optimization

### 1. Backend Optimizations
- Enable gzip compression
- Implement response caching
- Use connection pooling
- Monitor memory usage

### 2. Frontend Optimizations
- Enable static file caching
- Use CDN for assets
- Implement service worker
- Optimize bundle size

## Security Configuration

### 1. HTTPS Setup
```bash
# Using Let's Encrypt with Certbot
sudo certbot --nginx -d your-domain.com
```

### 2. Firewall Configuration
```bash
# Allow only necessary ports
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

### 3. Environment Security
- Store API keys in secure environment variables
- Use secrets management services
- Implement proper CORS policies
- Enable security headers

## Monitoring and Logging

### 1. Application Monitoring
```bash
# Install monitoring tools
npm install -g pm2
pm2 install pm2-logrotate

# Monitor processes
pm2 monit
```

### 2. Log Management
```bash
# View logs
pm2 logs

# Log rotation
pm2 install pm2-logrotate
```

## Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   sudo lsof -i :5000
   sudo kill -9 <PID>
   ```

2. **Permission Denied**
   ```bash
   sudo chown -R $USER:$USER /path/to/app
   chmod +x deploy.sh
   ```

3. **Memory Issues**
   ```bash
   # Increase Node.js memory limit
   node --max-old-space-size=4096 dist/index.js
   ```

4. **API Key Issues**
   - Verify environment variables are loaded
   - Check API key validity
   - Ensure proper escaping in environment files

### Health Checks

Monitor application health using these endpoints:
- Backend: `GET /health`
- Frontend: Check if static files are served correctly

## Backup and Recovery

### 1. Configuration Backup
```bash
# Backup environment files
tar -czf config-backup.tar.gz .env* ecosystem.config.js
```

### 2. Application Backup
```bash
# Backup entire application
tar -czf app-backup.tar.gz --exclude=node_modules .
```

## Scaling Considerations

### Horizontal Scaling
- Use load balancers (nginx, HAProxy)
- Implement session management
- Consider microservices architecture

### Vertical Scaling
- Monitor resource usage
- Optimize memory allocation
- Use clustering for Node.js

## Support and Maintenance

### Regular Maintenance Tasks
1. Update dependencies monthly
2. Monitor security vulnerabilities
3. Review and rotate API keys
4. Check disk space and logs
5. Performance monitoring and optimization

### Emergency Procedures
1. Application rollback procedures
2. Database recovery (if applicable)
3. Emergency contact information
4. Incident response plan

For additional support, refer to the technical documentation and API reference guides.
