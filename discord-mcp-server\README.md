# Discord MCP Server

A production-ready **Model Context Protocol (MCP) server** that enables AI models to interact with Discord through secure, authenticated tools. Built for enterprise-grade Discord automation and AI integration.

## 🚀 Features

### Core Discord Tools
- **5 Comprehensive Tools** for complete Discord interaction
- **Rich Message Support** with embeds, fields, and formatting
- **Advanced Search** with filters and pagination
- **Moderation Capabilities** with audit trails
- **Channel Management** with detailed information retrieval

### Enterprise Security
- **Multi-tenant Architecture** with complete isolation
- **API Key Authentication** with secure hashing
- **Permission-based Access Control** (RBAC)
- **Rate Limiting** with sliding window algorithm
- **Input Validation** and sanitization
- **Comprehensive Audit Logging** for compliance

### Development & Operations
- **MCP Inspector** for real-time debugging
- **Environment Validation** with automated checks
- **Comprehensive Testing** with >80% coverage
- **TypeScript** with strict type safety
- **Structured Logging** with correlation IDs
- **Production Deployment** guides and configurations

## 🛠️ Discord Tools

### 1. **send_message**
Send messages to Discord channels with rich formatting support.

**Features:**
- Plain text and rich embed messages
- Custom colors, fields, and formatting
- Input validation and content sanitization
- Permission checking and error handling

**Example:**
```json
{
  "channel_id": "123456789012345678",
  "content": "Hello from MCP!",
  "embed": {
    "title": "Status Update",
    "description": "System operational",
    "color": 65280,
    "fields": [{"name": "Uptime", "value": "99.9%"}]
  }
}
```

### 2. **get_messages**
Retrieve message history with advanced pagination and filtering.

**Features:**
- Configurable message limits (1-100)
- Before/after message ID filtering
- Efficient pagination with metadata
- Message formatting and author information

**Example:**
```json
{
  "channel_id": "123456789012345678",
  "limit": 50,
  "before": "987654321098765432"
}
```

### 3. **get_channel_info**
Get comprehensive channel information and metadata.

**Features:**
- Support for all Discord channel types
- Permission analysis and bot capabilities
- Recent activity statistics
- Guild and member information

**Example:**
```json
{
  "channel_id": "123456789012345678"
}
```

### 4. **search_messages**
Advanced message search with multiple filters and ranking.

**Features:**
- Full-text search with relevance scoring
- Date range and author filtering
- Cross-channel search capabilities
- Configurable result limits and pagination

**Example:**
```json
{
  "query": "important announcement",
  "channel_id": "123456789012345678",
  "date_from": "2024-01-01T00:00:00.000Z",
  "limit": 25
}
```

### 5. **moderate_content**
Comprehensive moderation actions with audit trails.

**Features:**
- Delete messages with reason tracking
- User timeouts with configurable duration
- Ban and kick users with audit logs
- Permission validation and error handling

**Example:**
```json
{
  "action": "timeout_user",
  "target_id": "123456789012345678",
  "reason": "Inappropriate behavior",
  "duration": 3600
}
```

## 🚀 Quick Start

### Prerequisites
- **Node.js 18+**
- **Discord Bot Token** and **Application ID**
- **Database** (SQLite for dev, PostgreSQL for prod)

### Installation

1. **Clone and install dependencies:**
   ```bash
   git clone <repository-url>
   cd discord-mcp-server
   npm install
   ```

2. **Configure environment:**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` with your Discord credentials:
   ```env
   DISCORD_BOT_TOKEN=your_discord_bot_token_here
   DISCORD_APPLICATION_ID=your_discord_application_id_here
   ```

3. **Start development server:**
   ```bash
   npm run dev
   ```

4. **Access MCP Inspector:**
   Open `http://localhost:3001` for real-time debugging

### Production Deployment

1. **Build for production:**
   ```bash
   npm run build
   ```

2. **Start production server:**
   ```bash
   npm start
   ```

3. **Configure database:**
   ```env
   DATABASE_URL=postgresql://user:pass@host:port/db
   ```

## 🧪 Testing

### Run Test Suite
```bash
# All tests
npm test

# With coverage report
npm run test:coverage

# Unit tests only
npm run test:unit

# Integration tests only
npm run test:integration

# Watch mode for development
npm run test:watch
```

### Test Coverage
- **Unit Tests**: Individual component testing with mocks
- **Integration Tests**: End-to-end MCP server testing
- **Security Tests**: Authentication and authorization
- **Performance Tests**: Load and stress testing
- **Coverage**: >80% across all modules

## 🔒 Security

### Authentication
- **API Key Based**: Secure SHA-256 hashing
- **Multi-tenant Support**: Complete tenant isolation
- **Permission System**: Granular RBAC per tenant
- **Token Validation**: Comprehensive validation pipeline

### Input Validation
- **Schema Validation**: Zod-based input validation
- **Sanitization**: XSS and injection prevention
- **Rate Limiting**: Sliding window per tenant/tool
- **Content Filtering**: Discord content policy compliance

### Audit Logging
- **Complete Tracking**: All operations logged
- **Security Events**: Authentication and authorization
- **Performance Metrics**: Response times and errors
- **Compliance**: Configurable retention policies

## 📊 Performance

### Metrics
- **Response Time**: <100ms average for tool execution
- **Throughput**: 100+ requests/minute per tenant
- **Memory Usage**: <100MB baseline footprint
- **Database Performance**: <10ms query response time

### Optimization
- **Caching Layer**: Redis-compatible caching
- **Connection Pooling**: Efficient database connections
- **Rate Limiting**: Prevents API abuse
- **Lazy Loading**: On-demand resource loading

## 🔧 Configuration

### Environment Variables
```env
# Discord Configuration
DISCORD_BOT_TOKEN=your_bot_token
DISCORD_APPLICATION_ID=your_app_id

# Server Configuration
MCP_SERVER_PORT=3000
MCP_INSPECTOR_PORT=3001

# Database Configuration
DATABASE_URL=sqlite:./data/discord_mcp.db
DATABASE_MAX_CONNECTIONS=10

# Security Configuration
JWT_SECRET=your_jwt_secret
API_KEYS=key1:tenant1,key2:tenant2
ENCRYPTION_KEY=your_32_char_key

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/discord-mcp-server.log
```

### Database Setup
```bash
# SQLite (Development)
mkdir -p data
touch data/discord_mcp.db

# PostgreSQL (Production)
createdb discord_mcp_production
```

## 📚 Documentation

### API Documentation
- **[API Reference](docs/API.md)** - Complete API documentation
- **[Deployment Guide](docs/DEPLOYMENT.md)** - Production deployment

### Development Guides
- **[Testing Guide](../tests/README.md)** - Testing procedures
- **[Security Guide](docs/SECURITY.md)** - Security best practices

## 🐛 Debugging

### MCP Inspector
Access the real-time debugging interface:
```bash
npm run inspector
# Open http://localhost:3001
```

**Features:**
- Real-time request/response monitoring
- Performance metrics and analytics
- Error tracking and debugging
- API testing interface

### Logging
```bash
# View logs
tail -f logs/discord-mcp-server.log

# Debug mode
LOG_LEVEL=debug npm run dev
```

## 🚀 Deployment

### Local Development
```bash
npm run dev          # Development with hot reload
npm run inspector    # With MCP Inspector enabled
```

### Production
```bash
npm run build        # Build TypeScript
npm start           # Start production server
```

### Process Management
```bash
# PM2
pm2 start ecosystem.config.js

# systemd
sudo systemctl start discord-mcp-server
```

### Cloud Platforms
- **Railway**: One-click deployment
- **Heroku**: Buildpack support
- **AWS EC2**: Complete deployment guide
- **Docker**: Container support

## 📈 Monitoring

### Health Checks
```bash
curl http://localhost:3000/health
```

### Metrics Endpoints
- `/health` - Server health status
- `/metrics` - Performance metrics
- `/status` - Detailed system status

### Alerting
- **Error Rate**: >5% error rate alerts
- **Response Time**: >500ms response alerts
- **Memory Usage**: >80% memory alerts
- **Database**: Connection and query alerts

## 🤝 Contributing

### Development Setup
1. Fork the repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Install dependencies: `npm install`
4. Run tests: `npm test`
5. Commit changes: `git commit -m 'Add amazing feature'`
6. Push branch: `git push origin feature/amazing-feature`
7. Open Pull Request

### Code Standards
- **TypeScript**: Strict type checking
- **ESLint**: Code style enforcement
- **Prettier**: Code formatting
- **Jest**: Testing framework
- **Conventional Commits**: Commit message format

## 📄 License

This project is part of the W4D3 Understanding RAG assignment and is created for educational purposes.

## 🆘 Support

### Documentation
- Check the `docs/` folder for detailed guides
- Review the test suite for usage examples
- Use MCP Inspector for real-time debugging

### Issues
- **GitHub Issues**: Bug reports and feature requests
- **Discussions**: Questions and community support
- **Security**: Report security issues privately

## 👨‍💻 Author

**Shaoni Dutta**
- GitHub: [@shaonidutta](https://github.com/shaonidutta)
- Assignment: W4D3 Understanding RAG - Question 1

---

**Built with ❤️ for Discord AI Integration**
