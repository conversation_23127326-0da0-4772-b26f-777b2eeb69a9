import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { AuthManager } from '../../src/auth/manager.js';
import { testUtils } from '../setup.js';
import { AuthenticationError, AuthorizationError } from '../../src/types/index.js';

describe('AuthManager', () => {
  let authManager: AuthManager;

  beforeEach(async () => {
    authManager = new AuthManager();
    
    // Create test tenant and API key
    await testUtils.createTestTenant('test-tenant');
    await testUtils.createTestApiKey('test-tenant', 'test-api-key');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('API Key Validation', () => {
    it('should validate correct API key', async () => {
      const mockRequest = testUtils.createMockMCPRequest('send_message', {}, 'test-api-key');
      
      const result = await authManager.validateRequest(mockRequest);
      
      expect(result.valid).toBe(true);
      expect(result.tenantId).toBe('test-tenant');
      expect(result.permissions).toBeDefined();
    });

    it('should reject invalid API key', async () => {
      const mockRequest = testUtils.createMockMCPRequest('send_message', {}, 'invalid-key');
      
      const result = await authManager.validateRequest(mockRequest);
      
      expect(result.valid).toBe(false);
      expect(result.tenantId).toBeUndefined();
    });

    it('should reject missing API key', async () => {
      const mockRequest = {
        method: 'tools/call',
        params: {
          name: 'send_message',
          arguments: {}
        }
      };
      
      const result = await authManager.validateRequest(mockRequest);
      
      expect(result.valid).toBe(false);
    });

    it('should handle expired API key', async () => {
      // Create expired API key
      const expiredKey = await testUtils.createTestApiKey('test-tenant', 'expired-key');
      expiredKey.expiresAt = new Date(Date.now() - 1000); // Expired 1 second ago
      
      const mockRequest = testUtils.createMockMCPRequest('send_message', {}, 'expired-key');
      
      const result = await authManager.validateRequest(mockRequest);
      
      expect(result.valid).toBe(false);
    });
  });

  describe('Permission Checking', () => {
    it('should allow access with correct permissions', () => {
      const permissions = [
        {
          resource: 'discord',
          actions: ['send_message', 'get_messages']
        }
      ];
      
      const hasPermission = authManager.hasPermission(permissions, 'discord', 'send_message');
      
      expect(hasPermission).toBe(true);
    });

    it('should deny access without correct permissions', () => {
      const permissions = [
        {
          resource: 'discord',
          actions: ['get_messages'] // Missing send_message
        }
      ];
      
      const hasPermission = authManager.hasPermission(permissions, 'discord', 'send_message');
      
      expect(hasPermission).toBe(false);
    });

    it('should deny access for wrong resource', () => {
      const permissions = [
        {
          resource: 'other',
          actions: ['send_message']
        }
      ];
      
      const hasPermission = authManager.hasPermission(permissions, 'discord', 'send_message');
      
      expect(hasPermission).toBe(false);
    });

    it('should handle empty permissions array', () => {
      const permissions: any[] = [];
      
      const hasPermission = authManager.hasPermission(permissions, 'discord', 'send_message');
      
      expect(hasPermission).toBe(false);
    });
  });

  describe('Tenant Management', () => {
    it('should create new tenant successfully', async () => {
      const tenantData = {
        id: 'new-tenant',
        name: 'New Test Tenant',
        discordBotToken: 'new.bot.token',
        permissions: [
          {
            resource: 'discord',
            actions: ['send_message']
          }
        ],
        quotas: {
          maxRequestsPerMinute: 30,
          maxRequestsPerHour: 500,
          maxMessageHistory: 50,
          maxSearchResults: 25
        }
      };

      const tenant = await authManager.createTenant(tenantData);

      expect(tenant.id).toBe('new-tenant');
      expect(tenant.name).toBe('New Test Tenant');
      expect(tenant.permissions).toEqual(tenantData.permissions);
      expect(tenant.quotas).toEqual(tenantData.quotas);
    });

    it('should retrieve existing tenant', async () => {
      const tenant = await authManager.getTenant('test-tenant');

      expect(tenant).toBeDefined();
      expect(tenant!.id).toBe('test-tenant');
      expect(tenant!.name).toBe('Test Tenant test-tenant');
    });

    it('should return null for non-existent tenant', async () => {
      const tenant = await authManager.getTenant('non-existent');

      expect(tenant).toBeNull();
    });
  });

  describe('API Key Management', () => {
    it('should create new API key successfully', async () => {
      const keyData = {
        id: 'new-key-id',
        key: 'new-test-key',
        tenantId: 'test-tenant',
        name: 'New Test Key',
        permissions: [
          {
            resource: 'discord',
            actions: ['send_message']
          }
        ]
      };

      const apiKey = await authManager.createApiKey(keyData);

      expect(apiKey.id).toBe('new-key-id');
      expect(apiKey.tenantId).toBe('test-tenant');
      expect(apiKey.name).toBe('New Test Key');
    });

    it('should validate API key format', async () => {
      const validKey = 'test-api-key';
      const keyData = await authManager.validateApiKey(validKey);

      expect(keyData).toBeDefined();
      expect(keyData!.tenantId).toBe('test-tenant');
    });

    it('should return null for invalid API key', async () => {
      const invalidKey = 'invalid-key';
      const keyData = await authManager.validateApiKey(invalidKey);

      expect(keyData).toBeNull();
    });
  });

  describe('Audit Logging', () => {
    it('should log audit events successfully', async () => {
      await authManager.logAuditEvent(
        'test-tenant',
        'TEST_ACTION',
        'test_resource',
        'resource_id',
        { test: 'data' },
        'user_id',
        '127.0.0.1',
        'test-user-agent'
      );

      const auditLogs = await authManager.getAuditLogs('test-tenant', 10);

      expect(auditLogs).toHaveLength(1);
      expect(auditLogs[0].action).toBe('TEST_ACTION');
      expect(auditLogs[0].tenantId).toBe('test-tenant');
      expect(auditLogs[0].resource).toBe('test_resource');
    });

    it('should retrieve audit logs with pagination', async () => {
      // Create multiple audit log entries
      for (let i = 0; i < 5; i++) {
        await authManager.logAuditEvent(
          'test-tenant',
          `TEST_ACTION_${i}`,
          'test_resource',
          `resource_${i}`,
          { index: i }
        );
      }

      const firstPage = await authManager.getAuditLogs('test-tenant', 3, 0);
      const secondPage = await authManager.getAuditLogs('test-tenant', 3, 3);

      expect(firstPage).toHaveLength(3);
      expect(secondPage).toHaveLength(2);
      expect(firstPage[0].action).not.toBe(secondPage[0].action);
    });
  });

  describe('Caching', () => {
    it('should cache tenant data', async () => {
      // First call should hit database
      const tenant1 = await authManager.getTenant('test-tenant');
      
      // Second call should use cache
      const tenant2 = await authManager.getTenant('test-tenant');

      expect(tenant1).toEqual(tenant2);
    });

    it('should cache API key data', async () => {
      // First validation should hit database
      const key1 = await authManager.validateApiKey('test-api-key');
      
      // Second validation should use cache
      const key2 = await authManager.validateApiKey('test-api-key');

      expect(key1).toEqual(key2);
    });

    it('should cleanup expired cache entries', () => {
      // This would test the cache cleanup functionality
      // Implementation depends on how cache expiry is handled
      authManager.cleanupCache();
      
      // Verify cache is cleaned (this is a simplified test)
      expect(true).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors gracefully', async () => {
      // Mock database error
      jest.spyOn(console, 'error').mockImplementation(() => {});
      
      // This would test error handling when database is unavailable
      // Implementation depends on specific error handling strategy
      
      const result = await authManager.validateRequest({
        method: 'tools/call',
        params: { name: 'test', arguments: {} }
      });

      expect(result.valid).toBe(false);
    });

    it('should handle malformed requests', async () => {
      const malformedRequest = {
        // Missing required fields
      };

      const result = await authManager.validateRequest(malformedRequest);

      expect(result.valid).toBe(false);
    });
  });

  describe('Security Features', () => {
    it('should hash API keys securely', () => {
      const apiKey = 'test-api-key';
      const hash1 = authManager['hashApiKey'](apiKey);
      const hash2 = authManager['hashApiKey'](apiKey);

      // Same input should produce same hash
      expect(hash1).toBe(hash2);
      
      // Hash should not contain original key
      expect(hash1).not.toContain(apiKey);
      
      // Hash should be of expected length (SHA-256 = 64 hex chars)
      expect(hash1).toHaveLength(64);
    });

    it('should extract API key from different request formats', () => {
      const requests = [
        { meta: { apiKey: 'key-from-meta' } },
        { params: { apiKey: 'key-from-params' } }
      ];

      for (const request of requests) {
        const extractedKey = authManager['extractApiKeyFromRequest'](request);
        expect(extractedKey).toBeDefined();
      }
    });
  });
});
