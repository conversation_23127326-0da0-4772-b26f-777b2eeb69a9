import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, X, FileText, AlertCircle } from 'lucide-react';

interface TextInput {
  id: string;
  content: string;
  label: string;
}

interface TextInputManagerProps {
  onTextsChange: (texts: TextInput[]) => void;
  maxTexts?: number;
  minTexts?: number;
}

const TextInputManager: React.FC<TextInputManagerProps> = ({
  onTextsChange,
  maxTexts = 10,
  minTexts = 2,
}) => {
  const [texts, setTexts] = useState<TextInput[]>([
    { id: '1', content: '', label: 'Text 1' },
    { id: '2', content: '', label: 'Text 2' },
  ]);

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Validate individual text input
  const validateText = useCallback((content: string): string | null => {
    if (!content.trim()) {
      return 'Text content is required';
    }
    if (content.trim().length < 10) {
      return 'Text must be at least 10 characters long';
    }
    if (content.length > 10000) {
      return 'Text must be less than 10,000 characters';
    }
    return null;
  }, []);

  // Update text content
  const updateText = useCallback((id: string, field: 'content' | 'label', value: string) => {
    const updatedTexts = texts.map(text =>
      text.id === id ? { ...text, [field]: value } : text
    );
    setTexts(updatedTexts);

    // Validate if updating content
    if (field === 'content') {
      const error = validateText(value);
      setErrors(prev => ({
        ...prev,
        [id]: error || '',
      }));
    }

    // Notify parent component
    onTextsChange(updatedTexts);
  }, [texts, onTextsChange, validateText]);

  // Add new text input
  const addText = useCallback(() => {
    if (texts.length >= maxTexts) return;

    const newId = Date.now().toString();
    const newText: TextInput = {
      id: newId,
      content: '',
      label: `Text ${texts.length + 1}`,
    };

    const updatedTexts = [...texts, newText];
    setTexts(updatedTexts);
    onTextsChange(updatedTexts);
  }, [texts, maxTexts, onTextsChange]);

  // Remove text input
  const removeText = useCallback((id: string) => {
    if (texts.length <= minTexts) return;

    const updatedTexts = texts.filter(text => text.id !== id);
    setTexts(updatedTexts);
    
    // Remove error for deleted text
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[id];
      return newErrors;
    });

    onTextsChange(updatedTexts);
  }, [texts, minTexts, onTextsChange]);

  // Get validation status
  const getValidationStatus = () => {
    const hasErrors = Object.values(errors).some(error => error);
    const hasEmptyTexts = texts.some(text => !text.content.trim());
    const hasValidTexts = texts.filter(text => text.content.trim().length >= 10).length >= minTexts;
    
    return {
      isValid: !hasErrors && !hasEmptyTexts && hasValidTexts,
      hasErrors,
      hasEmptyTexts,
      validTextCount: texts.filter(text => text.content.trim().length >= 10).length,
    };
  };

  const validationStatus = getValidationStatus();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <FileText className="h-5 w-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">Text Inputs</h3>
          <span className="text-sm text-gray-500">
            ({texts.length}/{maxTexts})
          </span>
        </div>
        
        {/* Validation Status */}
        <div className="flex items-center space-x-2">
          {validationStatus.hasErrors && (
            <div className="flex items-center space-x-1 text-red-600">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">Validation errors</span>
            </div>
          )}
          {validationStatus.isValid && (
            <div className="flex items-center space-x-1 text-green-600">
              <div className="h-2 w-2 bg-green-600 rounded-full"></div>
              <span className="text-sm">Ready for analysis</span>
            </div>
          )}
        </div>
      </div>

      {/* Text Inputs */}
      <div className="space-y-4">
        <AnimatePresence>
          {texts.map((text, index) => (
            <motion.div
              key={text.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, ease: 'easeInOut' }}
              className="relative"
            >
              <div className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                {/* Header */}
                <div className="flex items-center justify-between p-4 border-b border-gray-100">
                  <input
                    type="text"
                    value={text.label}
                    onChange={(e) => updateText(text.id, 'label', e.target.value)}
                    className="text-sm font-medium text-gray-900 bg-transparent border-none outline-none focus:ring-0"
                    placeholder="Text label"
                  />
                  
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-gray-500">
                      {text.content.length} chars
                    </span>
                    {texts.length > minTexts && (
                      <button
                        onClick={() => removeText(text.id)}
                        className="p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-colors duration-200"
                        title="Remove text"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </div>

                {/* Content */}
                <div className="p-4">
                  <textarea
                    value={text.content}
                    onChange={(e) => updateText(text.id, 'content', e.target.value)}
                    placeholder="Enter your text here for plagiarism analysis..."
                    className={`w-full h-32 p-3 border rounded-lg resize-none transition-colors duration-200 ${
                      errors[text.id]
                        ? 'border-red-300 focus:border-red-500 focus:ring-red-200'
                        : 'border-gray-300 focus:border-blue-500 focus:ring-blue-200'
                    } focus:ring-2 focus:outline-none`}
                  />
                  
                  {/* Error Message */}
                  {errors[text.id] && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="mt-2 flex items-center space-x-1 text-red-600"
                    >
                      <AlertCircle className="h-4 w-4" />
                      <span className="text-sm">{errors[text.id]}</span>
                    </motion.div>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Add Text Button */}
      {texts.length < maxTexts && (
        <motion.button
          onClick={addText}
          className="w-full p-4 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-blue-400 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200 flex items-center justify-center space-x-2"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <Plus className="h-5 w-5" />
          <span>Add Another Text</span>
        </motion.button>
      )}

      {/* Summary */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-blue-600">{texts.length}</div>
            <div className="text-sm text-gray-600">Total Texts</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-600">
              {validationStatus.validTextCount}
            </div>
            <div className="text-sm text-gray-600">Valid Texts</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-purple-600">
              {Math.max(0, (texts.length * (texts.length - 1)) / 2)}
            </div>
            <div className="text-sm text-gray-600">Comparisons</div>
          </div>
          <div>
            <div className={`text-2xl font-bold ${validationStatus.isValid ? 'text-green-600' : 'text-red-600'}`}>
              {validationStatus.isValid ? '✓' : '✗'}
            </div>
            <div className="text-sm text-gray-600">Status</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TextInputManager;
