import { loggers } from '../utils/logger.js';
import { discordConfig } from '../config/index.js';

/**
 * Discord Bot Setup Utilities
 * Provides guidance and validation for Discord bot configuration
 */
export class DiscordBotSetup {

  /**
   * Generate Discord bot invitation URL
   */
  static generateInviteUrl(applicationId: string, permissions?: string[]): string {
    const baseUrl = 'https://discord.com/api/oauth2/authorize';
    const clientId = applicationId;
    const scope = 'bot';
    
    // Default permissions for MCP Discord bot
    const defaultPermissions = [
      'ViewChannels',        // 1024
      'SendMessages',        // 2048  
      'ReadMessageHistory',  // 65536
      'ManageMessages',      // 8192
      'EmbedLinks',          // 16384
      'AttachFiles',         // 32768
      'UseExternalEmojis',   // 262144
      'KickMembers',         // 2 (for moderation)
      'BanMembers',          // 4 (for moderation)
      'ModerateMembers'      // 1099511627776 (for timeouts)
    ];

    // Convert permission names to bit values
    const permissionBits = this.calculatePermissionBits(permissions || defaultPermissions);
    
    const params = new URLSearchParams({
      client_id: clientId,
      scope: scope,
      permissions: permissionBits.toString()
    });

    const inviteUrl = `${baseUrl}?${params.toString()}`;
    
    loggers.info('Generated Discord bot invite URL', {
      applicationId,
      permissions: permissionBits,
      url: inviteUrl
    });

    return inviteUrl;
  }

  /**
   * Calculate permission bits from permission names
   */
  private static calculatePermissionBits(permissions: string[]): number {
    const permissionMap: Record<string, number> = {
      'CreateInstantInvite': 1,
      'KickMembers': 2,
      'BanMembers': 4,
      'Administrator': 8,
      'ManageChannels': 16,
      'ManageGuild': 32,
      'AddReactions': 64,
      'ViewAuditLog': 128,
      'PrioritySpeaker': 256,
      'Stream': 512,
      'ViewChannels': 1024,
      'SendMessages': 2048,
      'SendTTSMessages': 4096,
      'ManageMessages': 8192,
      'EmbedLinks': 16384,
      'AttachFiles': 32768,
      'ReadMessageHistory': 65536,
      'MentionEveryone': 131072,
      'UseExternalEmojis': 262144,
      'ViewGuildInsights': 524288,
      'Connect': 1048576,
      'Speak': 2097152,
      'MuteMembers': 4194304,
      'DeafenMembers': 8388608,
      'MoveMembers': 16777216,
      'UseVAD': 33554432,
      'ChangeNickname': 67108864,
      'ManageNicknames': 134217728,
      'ManageRoles': 268435456,
      'ManageWebhooks': 536870912,
      'ManageEmojisAndStickers': 1073741824,
      'UseApplicationCommands': 2147483648,
      'RequestToSpeak': 4294967296,
      'ManageEvents': 8589934592,
      'ManageThreads': 17179869184,
      'CreatePublicThreads': 34359738368,
      'CreatePrivateThreads': 68719476736,
      'UseExternalStickers': 137438953472,
      'SendMessagesInThreads': 274877906944,
      'UseEmbeddedActivities': 549755813888,
      'ModerateMembers': 1099511627776
    };

    let bits = 0;
    for (const permission of permissions) {
      if (permissionMap[permission]) {
        bits |= permissionMap[permission];
      }
    }

    return bits;
  }

  /**
   * Validate Discord bot token format
   */
  static validateBotToken(token: string): { valid: boolean; error?: string } {
    if (!token) {
      return { valid: false, error: 'Bot token is required' };
    }

    // Discord bot tokens have a specific format
    const tokenPattern = /^[A-Za-z0-9_-]{24}\.[A-Za-z0-9_-]{6}\.[A-Za-z0-9_-]{27}$/;
    
    if (!tokenPattern.test(token)) {
      return { valid: false, error: 'Invalid bot token format' };
    }

    return { valid: true };
  }

  /**
   * Validate Discord application ID format
   */
  static validateApplicationId(applicationId: string): { valid: boolean; error?: string } {
    if (!applicationId) {
      return { valid: false, error: 'Application ID is required' };
    }

    // Discord application IDs are 17-19 digit numbers
    const idPattern = /^\d{17,19}$/;
    
    if (!idPattern.test(applicationId)) {
      return { valid: false, error: 'Invalid application ID format' };
    }

    return { valid: true };
  }

  /**
   * Generate setup instructions for Discord bot
   */
  static generateSetupInstructions(applicationId: string): string {
    const inviteUrl = this.generateInviteUrl(applicationId);
    
    return `
# Discord Bot Setup Instructions

## Step 1: Create Discord Application
1. Go to https://discord.com/developers/applications
2. Click "New Application"
3. Give your application a name (e.g., "MCP Discord Bot")
4. Save the Application ID: ${applicationId}

## Step 2: Create Bot User
1. Go to the "Bot" section in your application
2. Click "Add Bot"
3. Copy the bot token (keep this secret!)
4. Enable the following intents:
   - Message Content Intent
   - Server Members Intent
   - Guild Messages Intent

## Step 3: Configure Bot Permissions
The bot needs the following permissions:
- View Channels
- Send Messages
- Read Message History
- Manage Messages
- Embed Links
- Attach Files
- Use External Emojis
- Kick Members (for moderation)
- Ban Members (for moderation)
- Moderate Members (for timeouts)

## Step 4: Invite Bot to Server
Use this invite URL to add the bot to your Discord server:
${inviteUrl}

## Step 5: Configure Environment
Add these to your .env file:
DISCORD_BOT_TOKEN=your_bot_token_here
DISCORD_APPLICATION_ID=${applicationId}

## Step 6: Test Connection
Run the MCP server and check the logs for successful connection.

## Security Notes
- Never share your bot token publicly
- Regenerate the token if it's compromised
- Use environment variables for sensitive data
- Regularly review bot permissions and access
    `.trim();
  }

  /**
   * Check required Discord intents
   */
  static getRequiredIntents(): string[] {
    return [
      'GUILDS',
      'GUILD_MESSAGES', 
      'MESSAGE_CONTENT',
      'GUILD_MEMBERS'
    ];
  }

  /**
   * Validate Discord configuration
   */
  static validateConfiguration(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate bot token
    const tokenValidation = this.validateBotToken(discordConfig.botToken);
    if (!tokenValidation.valid) {
      errors.push(`Bot Token: ${tokenValidation.error}`);
    }

    // Validate application ID
    const appIdValidation = this.validateApplicationId(discordConfig.applicationId);
    if (!appIdValidation.valid) {
      errors.push(`Application ID: ${appIdValidation.error}`);
    }

    // Check required intents
    const requiredIntents = this.getRequiredIntents();
    const configuredIntents = discordConfig.intents;
    
    for (const intent of requiredIntents) {
      if (!configuredIntents.includes(intent)) {
        errors.push(`Missing required intent: ${intent}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Get Discord API status
   */
  static async getDiscordApiStatus(): Promise<{ status: string; incidents?: any[] }> {
    try {
      // This would typically call Discord's status API
      // For now, we'll return a mock response
      return {
        status: 'operational',
        incidents: []
      };
    } catch (error) {
      loggers.error('Failed to get Discord API status', error as Error);
      return {
        status: 'unknown',
        incidents: []
      };
    }
  }

  /**
   * Generate bot configuration summary
   */
  static generateConfigSummary(): any {
    const validation = this.validateConfiguration();
    
    return {
      applicationId: discordConfig.applicationId,
      botTokenConfigured: !!discordConfig.botToken,
      intents: discordConfig.intents,
      apiVersion: discordConfig.apiVersion,
      inviteUrl: this.generateInviteUrl(discordConfig.applicationId),
      validation: validation,
      setupInstructions: validation.valid ? null : this.generateSetupInstructions(discordConfig.applicationId)
    };
  }
}

export { DiscordBotSetup };
