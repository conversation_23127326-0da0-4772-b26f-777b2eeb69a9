#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { validateConfig, config } from './config/index.js';
import { loggers } from './utils/logger.js';
import { setupMCPServer } from './server/index.js';
import { MCPInspector } from './server/inspector.js';

/**
 * Discord MCP Server
 * A Model Context Protocol server for Discord integration
 */

async function main() {
  try {
    // Validate configuration
    validateConfig();
    
    loggers.info('Starting Discord MCP Server', {
      name: config.name,
      version: config.version,
      port: config.port
    });

    // Create MCP server instance
    const server = new Server(
      {
        name: config.name,
        version: config.version,
      },
      {
        capabilities: {
          tools: {},
          resources: {},
          prompts: {},
        },
      }
    );

    // Setup server with all tools and handlers
    await setupMCPServer(server);

    // Initialize MCP Inspector
    const inspector = new MCPInspector(server);
    await inspector.start();

    // Create transport
    const transport = new StdioServerTransport();

    // Connect server to transport
    await server.connect(transport);

    loggers.info('Discord MCP Server started successfully', {
      transport: 'stdio',
      capabilities: ['tools', 'resources', 'prompts'],
      inspector: {
        enabled: true,
        port: config.port
      }
    });

    // Handle graceful shutdown
    process.on('SIGINT', async () => {
      loggers.info('Received SIGINT, shutting down gracefully...');
      await server.close();
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      loggers.info('Received SIGTERM, shutting down gracefully...');
      await server.close();
      process.exit(0);
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      loggers.error('Uncaught exception', error);
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      loggers.error('Unhandled rejection', new Error(String(reason)), {
        promise: promise.toString()
      });
      process.exit(1);
    });

  } catch (error) {
    loggers.error('Failed to start Discord MCP Server', error as Error);
    process.exit(1);
  }
}

// Start the server
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}
