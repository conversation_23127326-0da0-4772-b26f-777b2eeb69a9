import { SimilarityService } from '../../services/similarityService';
import { EmbeddingVector } from '../../types';

describe('SimilarityService', () => {
  let similarityService: SimilarityService;

  beforeEach(() => {
    similarityService = new SimilarityService();
  });

  describe('calculateSimilarity', () => {
    it('should calculate cosine similarity between identical vectors', () => {
      const vector1 = [1, 2, 3];
      const vector2 = [1, 2, 3];

      const similarity = similarityService.calculateSimilarity(vector1, vector2, 'cosine');

      expect(similarity).toBeCloseTo(1.0, 5);
    });

    it('should calculate cosine similarity between orthogonal vectors', () => {
      const vector1 = [1, 0, 0];
      const vector2 = [0, 1, 0];

      const similarity = similarityService.calculateSimilarity(vector1, vector2, 'cosine');

      expect(similarity).toBeCloseTo(0.0, 5);
    });

    it('should calculate euclidean distance', () => {
      const vector1 = [0, 0, 0];
      const vector2 = [3, 4, 0];

      const distance = similarityService.calculateSimilarity(vector1, vector2, 'euclidean');

      expect(distance).toBeCloseTo(5.0, 5); // 3-4-5 triangle
    });

    it('should calculate manhattan distance', () => {
      const vector1 = [0, 0, 0];
      const vector2 = [3, 4, 0];

      const distance = similarityService.calculateSimilarity(vector1, vector2, 'manhattan');

      expect(distance).toBe(7); // |3-0| + |4-0| + |0-0| = 7
    });

    it('should handle zero vectors', () => {
      const vector1 = [0, 0, 0];
      const vector2 = [0, 0, 0];

      const similarity = similarityService.calculateSimilarity(vector1, vector2, 'cosine');

      expect(similarity).toBe(0);
    });

    it('should throw error for mismatched dimensions', () => {
      const vector1 = [1, 2, 3];
      const vector2 = [1, 2];

      expect(() => {
        similarityService.calculateSimilarity(vector1, vector2, 'cosine');
      }).toThrow('Vectors must have the same dimensions');
    });
  });

  describe('generateSimilarityMatrix', () => {
    it('should generate similarity matrix for simple embeddings', () => {
      const embeddings: EmbeddingVector[] = [
        {
          textId: 'text1',
          model: 'test-model',
          vector: [1, 0, 0],
          dimensions: 3,
          metadata: {
            processingTime: 100,
            timestamp: new Date()
          }
        },
        {
          textId: 'text2',
          model: 'test-model',
          vector: [0, 1, 0],
          dimensions: 3,
          metadata: {
            processingTime: 100,
            timestamp: new Date()
          }
        }
      ];

      const matrix = similarityService.generateSimilarityMatrix(embeddings, 0.5, 'cosine');

      expect(matrix).toBeDefined();
      expect(matrix.matrix).toBeDefined();
      expect(matrix.matrix.length).toBe(2);
      if (matrix.matrix[0]) {
        expect(matrix.matrix[0].length).toBe(2);

        // Self-similarity should be 1
        expect(matrix.matrix[0][0]).toBe(1);

        // Cross-similarity should be 0 (orthogonal vectors)
        expect(matrix.matrix[0][1]).toBeCloseTo(0, 5);
      }
      if (matrix.matrix[1]) {
        expect(matrix.matrix[1][1]).toBe(1);
        expect(matrix.matrix[1][0]).toBeCloseTo(0, 5);
      }
    });

    it('should handle empty embeddings array', () => {
      const embeddings: EmbeddingVector[] = [];

      const matrix = similarityService.generateSimilarityMatrix(embeddings, 0.8, 'cosine');

      expect(matrix).toBeDefined();
      expect(matrix.matrix).toEqual([]);
    });

    it('should filter embeddings by threshold', () => {
      const embeddings: EmbeddingVector[] = [
        {
          textId: 'text1',
          model: 'test-model',
          vector: [1, 0, 0],
          dimensions: 3,
          metadata: {
            processingTime: 100,
            timestamp: new Date()
          }
        },
        {
          textId: 'text2',
          model: 'test-model',
          vector: [1, 0, 0], // Identical vector
          dimensions: 3,
          metadata: {
            processingTime: 100,
            timestamp: new Date()
          }
        }
      ];

      const matrix = similarityService.generateSimilarityMatrix(embeddings, 0.9, 'cosine');

      expect(matrix).toBeDefined();
      expect(matrix.matrix.length).toBe(2);

      // Should have high similarity between identical vectors
      if (matrix.matrix[0] && matrix.matrix[1]) {
        expect(matrix.matrix[0][1]).toBeCloseTo(1.0, 5);
        expect(matrix.matrix[1][0]).toBeCloseTo(1.0, 5);
      }
    });
  });

  describe('calculatePairwiseSimilarities', () => {
    it('should calculate pairwise similarities', () => {
      const embeddings: EmbeddingVector[] = [
        {
          textId: 'text1',
          model: 'test-model',
          vector: [1, 0, 0],
          dimensions: 3,
          metadata: {
            processingTime: 100,
            timestamp: new Date()
          }
        },
        {
          textId: 'text2',
          model: 'test-model',
          vector: [0, 1, 0],
          dimensions: 3,
          metadata: {
            processingTime: 100,
            timestamp: new Date()
          }
        }
      ];

      const pairs = similarityService.calculatePairwiseSimilarities(embeddings, 'cosine');

      expect(pairs).toBeDefined();
      expect(pairs.length).toBeGreaterThan(0);
      
      const pair = pairs[0];
      expect(pair).toHaveProperty('textId1');
      expect(pair).toHaveProperty('textId2');
      expect(pair).toHaveProperty('similarity');
      expect(pair).toHaveProperty('model');
    });
  });
});
