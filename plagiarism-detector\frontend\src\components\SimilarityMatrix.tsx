import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Info, Download, Eye, EyeOff } from 'lucide-react';

interface SimilarityMatrixProps {
  matrix: number[][];
  textIds: string[];
  textLabels?: Record<string, string>;
  model: string;
  threshold: number;
  onCellClick?: (textId1: string, textId2: string, similarity: number) => void;
}

const SimilarityMatrix: React.FC<SimilarityMatrixProps> = ({
  matrix,
  textIds,
  textLabels = {},
  model,
  threshold,
  onCellClick,
}) => {
  const [hoveredCell, setHoveredCell] = useState<{ i: number; j: number } | null>(null);
  const [showValues, setShowValues] = useState(true);
  const [colorScheme, setColorScheme] = useState<'default' | 'heatmap' | 'threshold'>('default');

  // Calculate statistics
  const statistics = useMemo(() => {
    const values: number[] = [];
    for (let i = 0; i < matrix.length; i++) {
      for (let j = i + 1; j < matrix[i].length; j++) {
        values.push(matrix[i][j]);
      }
    }

    if (values.length === 0) return null;

    values.sort((a, b) => a - b);
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const median = values[Math.floor(values.length / 2)];
    const min = values[0];
    const max = values[values.length - 1];
    const aboveThreshold = values.filter(v => v >= threshold).length;

    return { mean, median, min, max, total: values.length, aboveThreshold };
  }, [matrix, threshold]);

  // Get color for similarity value
  const getColor = (similarity: number, i: number, j: number): string => {
    if (i === j) return 'bg-gray-100'; // Diagonal

    switch (colorScheme) {
      case 'heatmap':
        const intensity = Math.round(similarity * 255);
        return `rgb(${255 - intensity}, ${255 - intensity}, 255)`;
      
      case 'threshold':
        if (similarity >= threshold) {
          return similarity >= 0.9 ? 'bg-red-500' : 'bg-orange-400';
        }
        return similarity >= 0.5 ? 'bg-yellow-200' : 'bg-green-200';
      
      default:
        if (similarity >= 0.9) return 'bg-red-100 border-red-300';
        if (similarity >= 0.8) return 'bg-orange-100 border-orange-300';
        if (similarity >= 0.6) return 'bg-yellow-100 border-yellow-300';
        if (similarity >= 0.4) return 'bg-blue-100 border-blue-300';
        return 'bg-green-100 border-green-300';
    }
  };

  // Get text color for similarity value
  const getTextColor = (similarity: number): string => {
    if (colorScheme === 'heatmap') {
      return similarity > 0.5 ? 'text-white' : 'text-gray-900';
    }
    
    if (similarity >= threshold) {
      return 'text-red-800 font-semibold';
    }
    return 'text-gray-700';
  };

  // Format similarity value
  const formatSimilarity = (value: number): string => {
    return (value * 100).toFixed(1) + '%';
  };

  // Get label for text ID
  const getLabel = (textId: string): string => {
    return textLabels[textId] || textId;
  };

  // Export matrix data
  const exportMatrix = () => {
    const csvContent = [
      ['', ...textIds.map(getLabel)],
      ...matrix.map((row, i) => [getLabel(textIds[i]), ...row.map(val => val.toFixed(4))])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `similarity-matrix-${model}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  if (!matrix || matrix.length === 0) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
        <div className="text-gray-500">No similarity matrix data available</div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      {/* Header */}
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Similarity Matrix - {model}
            </h3>
            <p className="text-sm text-gray-600">
              Threshold: {formatSimilarity(threshold)}
            </p>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Color Scheme Selector */}
            <select
              value={colorScheme}
              onChange={(e) => setColorScheme(e.target.value as any)}
              className="text-sm border border-gray-300 rounded px-2 py-1"
            >
              <option value="default">Default</option>
              <option value="heatmap">Heatmap</option>
              <option value="threshold">Threshold</option>
            </select>

            {/* Toggle Values */}
            <button
              onClick={() => setShowValues(!showValues)}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors"
              title={showValues ? 'Hide values' : 'Show values'}
            >
              {showValues ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </button>

            {/* Export Button */}
            <button
              onClick={exportMatrix}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors"
              title="Export as CSV"
            >
              <Download className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Matrix */}
      <div className="p-4">
        <div className="overflow-auto">
          <div className="inline-block min-w-full">
            <table className="border-collapse">
              <thead>
                <tr>
                  <th className="w-32 h-8"></th>
                  {textIds.map((textId, j) => (
                    <th
                      key={textId}
                      className="w-16 h-8 text-xs font-medium text-gray-700 text-center border border-gray-200 bg-gray-50"
                      title={getLabel(textId)}
                    >
                      <div className="truncate px-1">
                        {getLabel(textId)}
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {matrix.map((row, i) => (
                  <tr key={textIds[i]}>
                    <td
                      className="w-32 h-16 text-xs font-medium text-gray-700 text-right pr-2 border border-gray-200 bg-gray-50"
                      title={getLabel(textIds[i])}
                    >
                      <div className="truncate">
                        {getLabel(textIds[i])}
                      </div>
                    </td>
                    {row.map((similarity, j) => (
                      <motion.td
                        key={`${i}-${j}`}
                        className={`w-16 h-16 text-center border border-gray-200 cursor-pointer relative ${getColor(similarity, i, j)}`}
                        whileHover={{ scale: 1.05, zIndex: 10 }}
                        onMouseEnter={() => setHoveredCell({ i, j })}
                        onMouseLeave={() => setHoveredCell(null)}
                        onClick={() => {
                          if (i !== j && onCellClick) {
                            onCellClick(textIds[i], textIds[j], similarity);
                          }
                        }}
                        style={colorScheme === 'heatmap' ? { backgroundColor: getColor(similarity, i, j) } : {}}
                      >
                        {showValues && (
                          <div className={`text-xs ${getTextColor(similarity)}`}>
                            {i === j ? '100%' : formatSimilarity(similarity)}
                          </div>
                        )}
                        
                        {/* Hover Tooltip */}
                        {hoveredCell?.i === i && hoveredCell?.j === j && i !== j && (
                          <motion.div
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            className="absolute z-20 bg-gray-900 text-white text-xs rounded px-2 py-1 -top-8 left-1/2 transform -translate-x-1/2 whitespace-nowrap"
                          >
                            {getLabel(textIds[i])} ↔ {getLabel(textIds[j])}: {formatSimilarity(similarity)}
                            <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
                          </motion.div>
                        )}
                      </motion.td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Statistics */}
      {statistics && (
        <div className="p-4 border-t border-gray-100 bg-gray-50">
          <div className="flex items-center space-x-1 mb-2">
            <Info className="h-4 w-4 text-blue-600" />
            <span className="text-sm font-medium text-gray-900">Statistics</span>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-6 gap-4 text-center">
            <div>
              <div className="text-lg font-semibold text-blue-600">
                {formatSimilarity(statistics.mean)}
              </div>
              <div className="text-xs text-gray-600">Average</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-green-600">
                {formatSimilarity(statistics.median)}
              </div>
              <div className="text-xs text-gray-600">Median</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-purple-600">
                {formatSimilarity(statistics.max)}
              </div>
              <div className="text-xs text-gray-600">Maximum</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-orange-600">
                {formatSimilarity(statistics.min)}
              </div>
              <div className="text-xs text-gray-600">Minimum</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-red-600">
                {statistics.aboveThreshold}
              </div>
              <div className="text-xs text-gray-600">Above Threshold</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-gray-600">
                {statistics.total}
              </div>
              <div className="text-xs text-gray-600">Total Pairs</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SimilarityMatrix;
