import { Request, Response } from 'express';
import { z } from 'zod';
import { v4 as uuidv4 } from 'uuid';
import { TextPreprocessorService } from '../services/textPreprocessor.js';
import { EmbeddingService } from '../services/embeddingService.js';
import { SimilarityService } from '../services/similarityService.js';
import { CloneDetectionService } from '../services/cloneDetectionService.js';
import { AnalysisRequest, AnalysisResponse, ApiResponse, ValidationError } from '../types/index.js';
import { similarityConfig } from '../utils/config.js';

// Request validation schemas
const TextInputSchema = z.object({
  id: z.string().optional(),
  content: z.string().min(10, 'Text must be at least 10 characters long'),
  label: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

const AnalysisRequestSchema = z.object({
  texts: z.array(TextInputSchema).min(2, 'At least 2 texts are required').max(10, 'Maximum 10 texts allowed'),
  options: z.object({
    models: z.array(z.string()).min(1, 'At least one model is required'),
    threshold: z.number().min(0).max(1).default(0.8),
    enableCloneDetection: z.boolean().default(true),
    enableModelComparison: z.boolean().default(true),
    preprocessingOptions: z.object({
      removeStopWords: z.boolean().default(false),
      stemming: z.boolean().default(false),
      lemmatization: z.boolean().default(true),
      removeNumbers: z.boolean().default(false),
      removePunctuation: z.boolean().default(false),
      toLowerCase: z.boolean().default(true),
      removeExtraWhitespace: z.boolean().default(true),
      minWordLength: z.number().min(1).default(1),
      customStopWords: z.array(z.string()).optional(),
    }).default({}),
  }),
});

/**
 * Analysis Controller
 * Handles the main plagiarism detection analysis workflow
 */
export class AnalysisController {
  private textPreprocessor: TextPreprocessorService;
  private embeddingService: EmbeddingService;
  private similarityService: SimilarityService;
  private cloneDetectionService: CloneDetectionService;

  constructor() {
    this.textPreprocessor = new TextPreprocessorService();
    this.embeddingService = new EmbeddingService();
    this.similarityService = new SimilarityService();
    this.cloneDetectionService = new CloneDetectionService();
  }

  /**
   * Main analysis endpoint
   */
  async analyzeTexts(req: Request, res: Response): Promise<void> {
    const requestId = uuidv4();
    const startTime = Date.now();

    try {
      // Validate request
      const validatedRequest = AnalysisRequestSchema.parse(req.body);
      
      // Assign IDs to texts if not provided
      validatedRequest.texts.forEach((text, index) => {
        if (!text.id) {
          text.id = `text_${index + 1}`;
        }
      });

      // Validate text lengths
      validatedRequest.texts.forEach(text => {
        this.textPreprocessor.validateTextInput(text, similarityConfig.maxTextLength);
      });

      console.log(`Starting analysis ${requestId} for ${validatedRequest.texts.length} texts`);

      // Step 1: Preprocess texts
      console.log('Step 1: Preprocessing texts...');
      const processedTexts = await this.textPreprocessor.processTexts(
        validatedRequest.texts,
        validatedRequest.options.preprocessingOptions
      );

      // Step 2: Generate embeddings for all models
      console.log('Step 2: Generating embeddings...');
      const embeddings = await this.embeddingService.generateMultiModelEmbeddings(
        processedTexts,
        validatedRequest.options.models
      );

      // Step 3: Calculate similarity matrices
      console.log('Step 3: Calculating similarity matrices...');
      const similarityMatrices = this.similarityService.generateMultiModelSimilarityMatrices(
        embeddings,
        validatedRequest.options.threshold
      );

      // Step 4: Detect clones (if enabled)
      let cloneDetectionResults: any[] = [];
      if (validatedRequest.options.enableCloneDetection) {
        console.log('Step 4: Detecting clones...');
        for (const [modelName, matrix] of Object.entries(similarityMatrices)) {
          const clones = this.cloneDetectionService.detectClones(
            processedTexts,
            matrix,
            validatedRequest.options.threshold
          );
          cloneDetectionResults.push(...clones);
        }
      }

      // Step 5: Model comparison (if enabled)
      let modelComparison;
      if (validatedRequest.options.enableModelComparison && validatedRequest.options.models.length > 1) {
        console.log('Step 5: Comparing models...');
        modelComparison = this.generateModelComparison(similarityMatrices, processedTexts);
      }

      // Calculate processing time
      const processingTime = Date.now() - startTime;

      // Generate summary
      const summary = this.generateSummary(
        processedTexts,
        similarityMatrices,
        cloneDetectionResults,
        validatedRequest.options.models,
        processingTime
      );

      // Prepare response
      const response: AnalysisResponse = {
        requestId,
        processedTexts,
        embeddings,
        similarityMatrices,
        cloneDetectionResults,
        modelComparison,
        summary,
        timestamp: new Date(),
      };

      const apiResponse: ApiResponse<AnalysisResponse> = {
        success: true,
        data: response,
        metadata: {
          requestId,
          timestamp: new Date(),
          processingTime,
        },
      };

      console.log(`Analysis ${requestId} completed in ${processingTime}ms`);
      res.json(apiResponse);

    } catch (error) {
      console.error(`Analysis ${requestId} failed:`, error);
      
      if (error instanceof z.ZodError) {
        const validationError: ApiResponse = {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid request data',
            details: error.errors,
          },
          metadata: {
            requestId,
            timestamp: new Date(),
            processingTime: Date.now() - startTime,
          },
        };
        res.status(400).json(validationError);
      } else {
        const serverError: ApiResponse = {
          success: false,
          error: {
            code: 'ANALYSIS_ERROR',
            message: error instanceof Error ? error.message : 'Analysis failed',
            details: error,
          },
          metadata: {
            requestId,
            timestamp: new Date(),
            processingTime: Date.now() - startTime,
          },
        };
        res.status(500).json(serverError);
      }
    }
  }

  /**
   * Get available models endpoint
   */
  async getAvailableModels(req: Request, res: Response): Promise<void> {
    try {
      const models = this.embeddingService.getAvailableModels();
      const response: ApiResponse = {
        success: true,
        data: models,
        metadata: {
          requestId: uuidv4(),
          timestamp: new Date(),
          processingTime: 0,
        },
      };
      res.json(response);
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          code: 'MODELS_ERROR',
          message: 'Failed to retrieve available models',
        },
      });
    }
  }

  /**
   * Health check endpoint
   */
  async healthCheck(req: Request, res: Response): Promise<void> {
    try {
      const health = {
        status: 'healthy',
        timestamp: new Date(),
        services: {
          textPreprocessor: 'operational',
          embeddingService: 'operational',
          similarityService: 'operational',
          cloneDetectionService: 'operational',
        },
        models: this.embeddingService.getAvailableModels().map(m => m.name),
      };

      res.json({
        success: true,
        data: health,
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          code: 'HEALTH_CHECK_ERROR',
          message: 'Health check failed',
        },
      });
    }
  }

  /**
   * Generate model comparison
   */
  private generateModelComparison(similarityMatrices: Record<string, any>, processedTexts: any[]): any {
    const textPairs: any[] = [];
    const modelPerformance: Record<string, any> = {};

    // Extract text pairs and their similarities across models
    const textIds = processedTexts.map(t => t.id);
    for (let i = 0; i < textIds.length; i++) {
      for (let j = i + 1; j < textIds.length; j++) {
        const pair = {
          textId1: textIds[i],
          textId2: textIds[j],
          similarities: {} as Record<string, number>,
        };

        for (const [modelName, matrix] of Object.entries(similarityMatrices)) {
          if (matrix.matrix && matrix.matrix[i] && matrix.matrix[i][j] !== undefined) {
            pair.similarities[modelName] = matrix.matrix[i][j];
          }
        }

        textPairs.push(pair);
      }
    }

    // Calculate model performance metrics
    for (const [modelName, matrix] of Object.entries(similarityMatrices)) {
      modelPerformance[modelName] = {
        averageSimilarity: matrix.metadata.averageSimilarity,
        processingTime: matrix.metadata.processingTime,
        clonesDetected: matrix.metadata.clonesDetected,
      };
    }

    // Determine best model (simple heuristic)
    const bestModel = Object.entries(modelPerformance).reduce((best, [name, perf]) => {
      const score = perf.averageSimilarity * 0.7 + (1 / Math.log(perf.processingTime + 1)) * 0.3;
      return score > best.score ? { name, score } : best;
    }, { name: '', score: -1 });

    return {
      textPairs,
      modelPerformance,
      recommendation: {
        bestModel: bestModel.name,
        reason: 'Best balance of accuracy and performance',
        confidence: 0.8,
      },
    };
  }

  /**
   * Generate analysis summary
   */
  private generateSummary(
    processedTexts: any[],
    similarityMatrices: Record<string, any>,
    cloneDetectionResults: any[],
    modelsUsed: string[],
    processingTime: number
  ): any {
    const totalTexts = processedTexts.length;
    const clonesFound = cloneDetectionResults.length;
    
    // Calculate average similarity across all models
    const allSimilarities = Object.values(similarityMatrices)
      .map(matrix => matrix.metadata.averageSimilarity)
      .filter(sim => !isNaN(sim));
    
    const averageSimilarity = allSimilarities.length > 0
      ? allSimilarities.reduce((sum, sim) => sum + sim, 0) / allSimilarities.length
      : 0;

    const recommendations = [];
    if (clonesFound > 0) {
      recommendations.push(`Found ${clonesFound} potential plagiarism cases requiring review`);
    }
    if (averageSimilarity > 0.7) {
      recommendations.push('High overall similarity detected - consider reviewing content originality');
    }
    if (recommendations.length === 0) {
      recommendations.push('No significant plagiarism concerns detected');
    }

    return {
      totalTexts,
      modelsUsed,
      processingTime,
      clonesFound,
      averageSimilarity,
      recommendations,
    };
  }
}
