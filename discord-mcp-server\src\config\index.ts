import dotenv from 'dotenv';
import { z } from 'zod';
import { ServerConfig } from '../types/index.js';

// Load environment variables
dotenv.config();

// Environment validation schema
const EnvSchema = z.object({
  // Discord Configuration
  DISCORD_BOT_TOKEN: z.string().min(1, "Discord bot token is required"),
  DISCORD_APPLICATION_ID: z.string().min(1, "Discord application ID is required"),
  
  // MCP Server Configuration
  MCP_SERVER_NAME: z.string().default("discord-mcp-server"),
  MCP_SERVER_VERSION: z.string().default("1.0.0"),
  MCP_SERVER_PORT: z.string().transform(Number).default("3000"),
  
  // Authentication & Security
  JWT_SECRET: z.string().min(32, "JWT secret must be at least 32 characters"),
  API_KEYS: z.string().min(1, "At least one API key must be configured"),
  ENCRYPTION_KEY: z.string().length(32, "Encryption key must be exactly 32 characters"),
  
  // Database Configuration
  DATABASE_URL: z.string().default("sqlite:./data/discord_mcp.db"),
  DATABASE_MAX_CONNECTIONS: z.string().transform(Number).default("10"),
  
  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: z.string().transform(Number).default("60000"),
  RATE_LIMIT_MAX_REQUESTS: z.string().transform(Number).default("100"),
  RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS: z.string().transform(val => val === 'true').default("false"),
  
  // Logging
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  LOG_FILE: z.string().default("./logs/discord-mcp-server.log"),
  LOG_MAX_SIZE: z.string().default("10m"),
  LOG_MAX_FILES: z.string().transform(Number).default("5"),
  
  // MCP Inspector
  MCP_INSPECTOR_PORT: z.string().transform(Number).default("3001"),
  MCP_INSPECTOR_ENABLED: z.string().transform(val => val === 'true').default("true"),
  
  // Discord API Configuration
  DISCORD_API_VERSION: z.string().default("10"),
  DISCORD_INTENTS: z.string().default("GUILDS,GUILD_MESSAGES,MESSAGE_CONTENT,GUILD_MEMBERS"),
  
  // Security Headers
  CORS_ORIGIN: z.string().default("http://localhost:3001"),
  CORS_CREDENTIALS: z.string().transform(val => val === 'true').default("true"),
  
  // Audit Logging
  AUDIT_LOG_ENABLED: z.string().transform(val => val === 'true').default("true"),
  AUDIT_LOG_RETENTION_DAYS: z.string().transform(Number).default("90"),
  
  // Performance
  CACHE_TTL_SECONDS: z.string().transform(Number).default("300"),
  MAX_MESSAGE_HISTORY: z.string().transform(Number).default("100"),
  MAX_SEARCH_RESULTS: z.string().transform(Number).default("50")
});

// Validate environment variables
const env = EnvSchema.parse(process.env);

// Parse API keys
const parseApiKeys = (apiKeysString: string): Map<string, string> => {
  const apiKeys = new Map<string, string>();
  const pairs = apiKeysString.split(',');
  
  for (const pair of pairs) {
    const [key, tenantId] = pair.split(':');
    if (key && tenantId) {
      apiKeys.set(key.trim(), tenantId.trim());
    }
  }
  
  if (apiKeys.size === 0) {
    throw new Error('No valid API keys found in configuration');
  }
  
  return apiKeys;
};

// Parse Discord intents
const parseDiscordIntents = (intentsString: string): string[] => {
  return intentsString.split(',').map(intent => intent.trim());
};

// Create server configuration
export const config: ServerConfig = {
  name: env.MCP_SERVER_NAME,
  version: env.MCP_SERVER_VERSION,
  port: env.MCP_SERVER_PORT,
  cors: {
    origin: env.CORS_ORIGIN,
    credentials: env.CORS_CREDENTIALS
  },
  rateLimit: {
    windowMs: env.RATE_LIMIT_WINDOW_MS,
    maxRequests: env.RATE_LIMIT_MAX_REQUESTS,
    skipSuccessfulRequests: env.RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS
  },
  database: {
    url: env.DATABASE_URL,
    maxConnections: env.DATABASE_MAX_CONNECTIONS
  },
  logging: {
    level: env.LOG_LEVEL,
    file: env.LOG_FILE,
    maxSize: env.LOG_MAX_SIZE,
    maxFiles: env.LOG_MAX_FILES
  },
  security: {
    jwtSecret: env.JWT_SECRET,
    encryptionKey: env.ENCRYPTION_KEY
  },
  discord: {
    apiVersion: env.DISCORD_API_VERSION,
    intents: parseDiscordIntents(env.DISCORD_INTENTS)
  }
};

// Export parsed configurations
export const discordConfig = {
  botToken: env.DISCORD_BOT_TOKEN,
  applicationId: env.DISCORD_APPLICATION_ID,
  apiVersion: env.DISCORD_API_VERSION,
  intents: parseDiscordIntents(env.DISCORD_INTENTS)
};

export const apiKeys = parseApiKeys(env.API_KEYS);

export const inspectorConfig = {
  port: env.MCP_INSPECTOR_PORT,
  enabled: env.MCP_INSPECTOR_ENABLED
};

export const auditConfig = {
  enabled: env.AUDIT_LOG_ENABLED,
  retentionDays: env.AUDIT_LOG_RETENTION_DAYS
};

export const performanceConfig = {
  cacheTtlSeconds: env.CACHE_TTL_SECONDS,
  maxMessageHistory: env.MAX_MESSAGE_HISTORY,
  maxSearchResults: env.MAX_SEARCH_RESULTS
};

// Validation helper
export const validateConfig = (): void => {
  try {
    EnvSchema.parse(process.env);
    console.log('✅ Configuration validation passed');
  } catch (error) {
    console.error('❌ Configuration validation failed:', error);
    process.exit(1);
  }
};

// Export environment for testing
export { env };
