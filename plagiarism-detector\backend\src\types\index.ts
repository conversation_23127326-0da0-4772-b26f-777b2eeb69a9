// Core types for the plagiarism detection system

export interface TextInput {
  id: string;
  content: string;
  label?: string;
  metadata?: Record<string, any>;
}

export interface ProcessedText {
  id: string;
  originalContent: string;
  processedContent: string;
  label?: string;
  metadata: {
    originalLength: number;
    processedLength: number;
    wordCount: number;
    sentenceCount: number;
    languageDetected?: string;
    processingSteps: string[];
    timestamp: Date;
  };
}

export interface EmbeddingVector {
  textId: string;
  model: string;
  vector: number[];
  dimensions: number;
  metadata: {
    modelVersion?: string;
    processingTime: number;
    timestamp: Date;
  };
}

export interface SimilarityPair {
  textId1: string;
  textId2: string;
  similarity: number;
  model: string;
  metadata: {
    calculationMethod: 'cosine' | 'euclidean' | 'manhattan';
    processingTime: number;
    timestamp: Date;
  };
}

export interface SimilarityMatrix {
  textIds: string[];
  matrix: number[][];
  model: string;
  threshold: number;
  metadata: {
    totalComparisons: number;
    averageSimilarity: number;
    maxSimilarity: number;
    minSimilarity: number;
    clonesDetected: number;
    processingTime: number;
    timestamp: Date;
  };
}

export interface CloneDetectionResult {
  textId1: string;
  textId2: string;
  similarity: number;
  model: string;
  confidence: 'low' | 'medium' | 'high' | 'very_high';
  evidence: {
    commonPhrases: string[];
    structuralSimilarity: number;
    semanticSimilarity: number;
  };
}

export interface ModelComparison {
  textPairs: Array<{
    textId1: string;
    textId2: string;
    similarities: Record<string, number>;
  }>;
  modelPerformance: Record<string, {
    averageSimilarity: number;
    processingTime: number;
    clonesDetected: number;
    accuracy?: number;
  }>;
  recommendation: {
    bestModel: string;
    reason: string;
    confidence: number;
  };
}

export interface AnalysisRequest {
  texts: TextInput[];
  options: {
    models: string[];
    threshold: number;
    enableCloneDetection: boolean;
    enableModelComparison: boolean;
    preprocessingOptions: PreprocessingOptions;
  };
}

export interface PreprocessingOptions {
  removeStopWords: boolean;
  stemming: boolean;
  lemmatization: boolean;
  removeNumbers: boolean;
  removePunctuation: boolean;
  toLowerCase: boolean;
  removeExtraWhitespace: boolean;
  minWordLength: number;
  customStopWords?: string[];
}

export interface AnalysisResponse {
  requestId: string;
  processedTexts: ProcessedText[];
  embeddings: Record<string, EmbeddingVector[]>;
  similarityMatrices: Record<string, SimilarityMatrix>;
  cloneDetectionResults: CloneDetectionResult[];
  modelComparison?: ModelComparison;
  summary: {
    totalTexts: number;
    modelsUsed: string[];
    processingTime: number;
    clonesFound: number;
    averageSimilarity: number;
    recommendations: string[];
  };
  timestamp: Date;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata?: {
    requestId: string;
    timestamp: Date;
    processingTime: number;
  };
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Error types
export class PlagiarismDetectionError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500,
    public details?: any
  ) {
    super(message);
    this.name = 'PlagiarismDetectionError';
  }
}

export class ValidationError extends PlagiarismDetectionError {
  constructor(message: string, details?: any) {
    super(message, 'VALIDATION_ERROR', 400, details);
    this.name = 'ValidationError';
  }
}

export class EmbeddingError extends PlagiarismDetectionError {
  constructor(message: string, details?: any) {
    super(message, 'EMBEDDING_ERROR', 500, details);
    this.name = 'EmbeddingError';
  }
}

export class RateLimitError extends PlagiarismDetectionError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 'RATE_LIMIT_ERROR', 429);
    this.name = 'RateLimitError';
  }
}

// Model types
export interface EmbeddingModel {
  name: string;
  displayName: string;
  provider: 'openai' | 'huggingface' | 'sentence-transformers';
  dimensions: number;
  maxTokens: number;
  costPerToken?: number;
  description: string;
  strengths: string[];
  limitations: string[];
  recommendedFor: string[];
}

// Configuration types
export interface ModelConfig {
  openai: {
    model: string;
    apiKey: string;
    maxTokens: number;
  };
  sentenceTransformers: {
    primaryModel: string;
    alternativeModel: string;
    cacheDir?: string;
  };
  huggingface: {
    apiKey?: string;
    models: string[];
  };
}

// Utility types
export type SimilarityMethod = 'cosine' | 'euclidean' | 'manhattan' | 'jaccard';
export type ModelProvider = 'openai' | 'huggingface' | 'sentence-transformers';
export type ConfidenceLevel = 'low' | 'medium' | 'high' | 'very_high';
export type ProcessingStatus = 'pending' | 'processing' | 'completed' | 'failed';

// Request/Response validation schemas will be defined separately using Zod
