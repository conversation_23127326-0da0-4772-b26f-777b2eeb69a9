# 🔍 Plagiarism Detector - AI-Powered Semantic Similarity Analyzer

[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![React](https://img.shields.io/badge/React-18.2-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue.svg)](https://www.typescriptlang.org/)
[![OpenAI](https://img.shields.io/badge/OpenAI-API-orange.svg)](https://openai.com/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

A sophisticated web application that uses advanced AI embedding models to detect semantic similarity and potential plagiarism between text documents. Built with modern TypeScript, React, and Node.js, featuring multiple embedding models including OpenAI's text-embedding-ada-002 and sentence-transformers.

## ✨ Features

### 🧠 Advanced AI Analysis
- **Multiple Embedding Models**: OpenAI Ada-002, sentence-transformers (all-MiniLM-L6-v2, all-mpnet-base-v2)
- **Semantic Similarity Detection**: Goes beyond simple text matching to understand meaning
- **Configurable Thresholds**: Adjustable similarity thresholds for different use cases
- **Model Comparison**: Side-by-side performance analysis of different models

### 🔍 Comprehensive Detection
- **Clone Detection**: Advanced algorithms identifying various plagiarism types
- **Interactive Similarity Matrix**: Visual representation with color-coded percentages
- **Evidence Gathering**: Common phrases, structural similarities, semantic overlaps
- **Confidence Scoring**: Multi-factor confidence assessment for detected similarities

### 🎨 Modern User Interface
- **Dynamic Text Input**: Add/remove multiple texts with real-time validation
- **Responsive Design**: Seamless experience across desktop and mobile
- **Smooth Animations**: Polished UI with Framer Motion animations
- **Interactive Visualizations**: Hover effects, tooltips, and export capabilities

### ⚡ Performance & Scalability
- **Efficient Processing**: Optimized algorithms for fast similarity calculations
- **Rate Limiting**: Built-in protection against abuse
- **Comprehensive Error Handling**: User-friendly error messages and recovery
- **Smart Caching**: Intelligent caching strategies for improved performance

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- OpenAI API key (for text-embedding-ada-002)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd plagiarism-detector
   ```

2. **Install dependencies**
   ```bash
   # Backend
   cd backend
   npm install
   
   # Frontend
   cd ../frontend
   npm install
   ```

3. **Configure environment**
   ```bash
   # Create backend/.env
   echo "OPENAI_API_KEY=your_openai_api_key_here" > backend/.env
   ```

4. **Start development servers**
   ```bash
   # Terminal 1 - Backend
   cd backend
   npm run dev
   
   # Terminal 2 - Frontend
   cd frontend
   npm run dev
   ```

5. **Open your browser**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000

## 📖 Usage

### Basic Analysis
1. **Add Text Documents**: Use the dynamic input manager to add 2-10 text documents
2. **Configure Settings**: Choose embedding models, set similarity threshold (0.5-1.0)
3. **Run Analysis**: Click "Analyze Texts" to start the plagiarism detection
4. **Review Results**: Examine the interactive similarity matrix and clone detection results

### Advanced Features
- **Model Comparison**: Enable to compare performance across different embedding models
- **Clone Detection**: Advanced algorithms identify different types of plagiarism
- **Export Results**: Download similarity matrices as CSV files
- **Threshold Adjustment**: Fine-tune sensitivity for different use cases

### Supported Text Types
- **Academic Papers**: Research papers, essays, dissertations
- **Code Documentation**: Technical documentation, API docs
- **Creative Writing**: Articles, blog posts, creative content
- **Legal Documents**: Contracts, policies, legal text

## 🏗️ Architecture

### Backend (Node.js + TypeScript)
```
backend/
├── src/
│   ├── controllers/     # API request handlers
│   ├── services/        # Core business logic
│   │   ├── textPreprocessor.ts      # Text cleaning and normalization
│   │   ├── embeddingService.ts      # AI model integration
│   │   ├── similarityService.ts     # Similarity calculations
│   │   └── cloneDetectionService.ts # Plagiarism detection
│   ├── types/           # TypeScript definitions
│   ├── utils/           # Configuration utilities
│   └── app.ts           # Express application
```

### Frontend (React + TypeScript)
```
frontend/
├── src/
│   ├── components/      # Reusable UI components
│   ├── pages/           # Application pages
│   ├── hooks/           # Custom React hooks
│   ├── config/          # Configuration
│   └── styles/          # Tailwind CSS styles
```

## 🔧 API Reference

### Analyze Texts
```http
POST /api/analyze
Content-Type: application/json

{
  "texts": [
    {
      "content": "Your text content here...",
      "label": "Document 1"
    }
  ],
  "options": {
    "models": ["text-embedding-ada-002"],
    "threshold": 0.8,
    "enableCloneDetection": true
  }
}
```

### Get Available Models
```http
GET /api/models
```

### Health Check
```http
GET /health
```

For complete API documentation, see [API.md](API.md).

## 🧪 Testing

### Run Backend Tests
```bash
cd backend
npm test
npm run test:coverage
```

### Run Frontend Tests
```bash
cd frontend
npm test
npm run test:coverage
```

### Integration Tests
```bash
npm run test:integration
```

## 📊 Embedding Models

### OpenAI text-embedding-ada-002
- **Dimensions**: 1536
- **Best for**: Highest accuracy, production use
- **Cost**: $0.0001 per 1K tokens
- **Strengths**: Multilingual, large context window

### all-mpnet-base-v2 (Sentence Transformers)
- **Dimensions**: 768
- **Best for**: Balanced accuracy and speed
- **Cost**: Free (local processing)
- **Strengths**: Good performance, no API dependency

### all-MiniLM-L6-v2 (Sentence Transformers)
- **Dimensions**: 384
- **Best for**: Fast processing, development
- **Cost**: Free (local processing)
- **Strengths**: Lightweight, fast inference

## 🔒 Security & Privacy

- **No Data Storage**: Texts processed in memory only
- **API Key Security**: Environment variable configuration
- **Rate Limiting**: Protection against abuse
- **Input Validation**: Comprehensive request validation
- **Local Processing**: Sentence-transformers run locally

## 🚀 Development

### Development Mode
```bash
# Backend
cd backend
npm run dev

# Frontend
cd frontend
npm run dev
```

### Production Build
```bash
# Backend
cd backend
npm run build
npm run start

# Frontend
cd frontend
npm run build
npm run preview
```

## 📈 Performance

### Processing Speed
- **OpenAI API**: ~2 seconds per text
- **Local Models**: ~0.5 seconds per text
- **Similarity Matrix**: <100ms for 10x10 matrix

### Resource Usage
- **Memory**: ~500MB with local models
- **CPU**: Moderate during processing
- **Network**: Dependent on OpenAI usage

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow TypeScript best practices
- Write comprehensive tests
- Update documentation
- Follow conventional commit messages

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [OpenAI](https://openai.com/) for the powerful embedding API
- [Hugging Face](https://huggingface.co/) for sentence-transformers
- [React](https://reactjs.org/) and [Node.js](https://nodejs.org/) communities
- All contributors and testers

## 📞 Support

- **Documentation**: [PROJECT_SUMMARY.md](PROJECT_SUMMARY.md)
- **API Reference**: [API.md](API.md)
- **Deployment Guide**: [DEPLOYMENT.md](DEPLOYMENT.md)
- **Issues**: GitHub Issues
- **Discussions**: GitHub Discussions

---

**Built with ❤️ using TypeScript, React, and Node.js**
