import { Server as HttpServer } from 'http';
import express from 'express';
import cors from 'cors';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { inspectorConfig, config } from '../config/index.js';
import { loggers } from '../utils/logger.js';
import { rateLimiter } from '../middleware/rate-limiter.js';
import { db } from '../database/models.js';

/**
 * MCP Inspector Integration
 * Provides debugging interface and real-time monitoring
 */
export class MCPInspector {
  private httpServer: HttpServer | null = null;
  private app: express.Application;
  private connections: Map<string, any> = new Map();
  private requestLog: any[] = [];
  private readonly maxLogEntries = 1000;

  constructor(private mcpServer: Server) {
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
  }

  /**
   * Setup Express middleware
   */
  private setupMiddleware(): void {
    // CORS configuration
    this.app.use(cors({
      origin: config.cors.origin,
      credentials: config.cors.credentials
    }));

    // JSON parsing
    this.app.use(express.json({ limit: '10mb' }));

    // Request logging
    this.app.use((req, res, next) => {
      const startTime = Date.now();
      
      res.on('finish', () => {
        const duration = Date.now() - startTime;
        this.logRequest({
          method: req.method,
          url: req.url,
          status: res.statusCode,
          duration,
          timestamp: new Date().toISOString(),
          userAgent: req.get('User-Agent'),
          ip: req.ip
        });
      });
      
      next();
    });
  }

  /**
   * Setup API routes for inspector
   */
  private setupRoutes(): void {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: config.version
      });
    });

    // Server status
    this.app.get('/api/status', async (req, res) => {
      try {
        const status = await this.getServerStatus();
        res.json(status);
      } catch (error) {
        loggers.error('Failed to get server status', error as Error);
        res.status(500).json({ error: 'Failed to get server status' });
      }
    });

    // Connection information
    this.app.get('/api/connections', (req, res) => {
      const connections = Array.from(this.connections.values());
      res.json({
        total: connections.length,
        connections: connections
      });
    });

    // Request logs
    this.app.get('/api/requests', (req, res) => {
      const limit = parseInt(req.query.limit as string) || 100;
      const offset = parseInt(req.query.offset as string) || 0;
      
      const logs = this.requestLog
        .slice(offset, offset + limit)
        .reverse(); // Most recent first
      
      res.json({
        total: this.requestLog.length,
        logs: logs
      });
    });

    // Rate limit statistics
    this.app.get('/api/rate-limits', (req, res) => {
      const stats = rateLimiter.getStatistics();
      res.json(stats);
    });

    // Audit logs
    this.app.get('/api/audit-logs/:tenantId', async (req, res) => {
      try {
        const { tenantId } = req.params;
        const limit = parseInt(req.query.limit as string) || 100;
        const offset = parseInt(req.query.offset as string) || 0;
        
        const auditLogs = await db.getAuditLogs(tenantId, limit, offset);
        
        res.json({
          tenantId,
          total: auditLogs.length,
          logs: auditLogs
        });
      } catch (error) {
        loggers.error('Failed to get audit logs', error as Error);
        res.status(500).json({ error: 'Failed to get audit logs' });
      }
    });

    // Tool usage statistics
    this.app.get('/api/tools/stats', (req, res) => {
      const stats = this.getToolUsageStats();
      res.json(stats);
    });

    // Performance metrics
    this.app.get('/api/metrics', (req, res) => {
      const metrics = this.getPerformanceMetrics();
      res.json(metrics);
    });

    // Clear logs (for debugging)
    this.app.delete('/api/requests', (req, res) => {
      this.requestLog.length = 0;
      loggers.info('Request logs cleared via inspector');
      res.json({ message: 'Request logs cleared' });
    });

    // Reset rate limits (for debugging)
    this.app.delete('/api/rate-limits/:tenantId', (req, res) => {
      const { tenantId } = req.params;
      rateLimiter.resetTenant(tenantId);
      loggers.info('Rate limits reset via inspector', { tenantId });
      res.json({ message: `Rate limits reset for tenant: ${tenantId}` });
    });

    // Serve static inspector UI (if available)
    this.app.get('/', (req, res) => {
      res.json({
        name: 'Discord MCP Server Inspector',
        version: config.version,
        endpoints: [
          'GET /health',
          'GET /api/status',
          'GET /api/connections',
          'GET /api/requests',
          'GET /api/rate-limits',
          'GET /api/audit-logs/:tenantId',
          'GET /api/tools/stats',
          'GET /api/metrics'
        ]
      });
    });
  }

  /**
   * Start the inspector server
   */
  async start(): Promise<void> {
    if (!inspectorConfig.enabled) {
      loggers.info('MCP Inspector disabled');
      return;
    }

    return new Promise((resolve, reject) => {
      this.httpServer = this.app.listen(inspectorConfig.port, () => {
        loggers.info('MCP Inspector started', {
          port: inspectorConfig.port,
          url: `http://localhost:${inspectorConfig.port}`
        });
        resolve();
      });

      this.httpServer.on('error', (error) => {
        loggers.error('MCP Inspector failed to start', error);
        reject(error);
      });
    });
  }

  /**
   * Stop the inspector server
   */
  async stop(): Promise<void> {
    if (this.httpServer) {
      return new Promise((resolve) => {
        this.httpServer!.close(() => {
          loggers.info('MCP Inspector stopped');
          resolve();
        });
      });
    }
  }

  /**
   * Track a new connection
   */
  trackConnection(connectionId: string, metadata: any): void {
    this.connections.set(connectionId, {
      id: connectionId,
      connectedAt: new Date().toISOString(),
      lastActivity: new Date().toISOString(),
      ...metadata
    });

    loggers.debug('Connection tracked', { connectionId, metadata });
  }

  /**
   * Update connection activity
   */
  updateConnectionActivity(connectionId: string): void {
    const connection = this.connections.get(connectionId);
    if (connection) {
      connection.lastActivity = new Date().toISOString();
    }
  }

  /**
   * Remove connection tracking
   */
  removeConnection(connectionId: string): void {
    this.connections.delete(connectionId);
    loggers.debug('Connection removed', { connectionId });
  }

  /**
   * Log a request for monitoring
   */
  private logRequest(requestData: any): void {
    this.requestLog.push(requestData);
    
    // Keep only the most recent entries
    if (this.requestLog.length > this.maxLogEntries) {
      this.requestLog.shift();
    }
  }

  /**
   * Get comprehensive server status
   */
  private async getServerStatus(): Promise<any> {
    const memUsage = process.memoryUsage();
    
    return {
      server: {
        name: config.name,
        version: config.version,
        uptime: process.uptime(),
        startTime: new Date(Date.now() - process.uptime() * 1000).toISOString()
      },
      memory: {
        rss: Math.round(memUsage.rss / 1024 / 1024),
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
        external: Math.round(memUsage.external / 1024 / 1024)
      },
      connections: {
        total: this.connections.size,
        active: Array.from(this.connections.values()).filter(
          conn => Date.now() - new Date(conn.lastActivity).getTime() < 300000 // 5 minutes
        ).length
      },
      rateLimits: {
        activeEntries: rateLimiter.getActiveEntries()
      },
      requests: {
        total: this.requestLog.length,
        recent: this.requestLog.slice(-10)
      }
    };
  }

  /**
   * Get tool usage statistics
   */
  private getToolUsageStats(): any {
    const stats: Record<string, any> = {};
    
    // Analyze request logs for tool usage
    for (const request of this.requestLog) {
      if (request.url?.includes('/api/')) {
        const tool = request.url.split('/').pop();
        if (!stats[tool]) {
          stats[tool] = { count: 0, avgDuration: 0, errors: 0 };
        }
        
        stats[tool].count++;
        stats[tool].avgDuration = (stats[tool].avgDuration + request.duration) / 2;
        
        if (request.status >= 400) {
          stats[tool].errors++;
        }
      }
    }
    
    return stats;
  }

  /**
   * Get performance metrics
   */
  private getPerformanceMetrics(): any {
    const recentRequests = this.requestLog.slice(-100);
    
    const avgResponseTime = recentRequests.length > 0 
      ? recentRequests.reduce((sum, req) => sum + req.duration, 0) / recentRequests.length
      : 0;
    
    const errorRate = recentRequests.length > 0
      ? recentRequests.filter(req => req.status >= 400).length / recentRequests.length
      : 0;

    return {
      responseTime: {
        average: Math.round(avgResponseTime),
        recent: recentRequests.slice(-10).map(req => req.duration)
      },
      errorRate: Math.round(errorRate * 100),
      requestsPerMinute: this.calculateRequestsPerMinute(),
      systemLoad: {
        cpu: process.cpuUsage(),
        memory: process.memoryUsage()
      }
    };
  }

  /**
   * Calculate requests per minute
   */
  private calculateRequestsPerMinute(): number {
    const oneMinuteAgo = Date.now() - 60000;
    const recentRequests = this.requestLog.filter(
      req => new Date(req.timestamp).getTime() > oneMinuteAgo
    );
    
    return recentRequests.length;
  }
}

export { MCPInspector };
