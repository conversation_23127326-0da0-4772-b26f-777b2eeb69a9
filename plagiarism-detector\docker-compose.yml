version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: plagiarism-detector-backend
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - PORT=5000
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - HUGGINGFACE_API_TOKEN=${HUGGINGFACE_API_TOKEN}
      - CORS_ORIGIN=http://localhost:3000
    volumes:
      - ./backend/logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - plagiarism-network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: plagiarism-detector-frontend
    ports:
      - "80:80"
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - plagiarism-network

  # Optional: Redis for caching (if needed in future)
  # redis:
  #   image: redis:7-alpine
  #   container_name: plagiarism-detector-redis
  #   ports:
  #     - "6379:6379"
  #   volumes:
  #     - redis_data:/data
  #   restart: unless-stopped
  #   networks:
  #     - plagiarism-network

networks:
  plagiarism-network:
    driver: bridge

volumes:
  # redis_data:
  backend_logs:
    driver: local
