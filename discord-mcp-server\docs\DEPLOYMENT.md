
## Overview

This guide covers deploying the Discord MCP Server in various environments, from development to production.

## Prerequisites

- **Node.js** 18+ 
- **npm** or **yarn**
- **Discord Bot Token** and **Application ID**
- **Database** (SQLite for development, PostgreSQL for production)

## Environment Setup

### 1. <PERSON><PERSON> and <PERSON>stall

```bash
git clone <repository-url>
cd discord-mcp-server
npm install
```

### 2. Environment Configuration

Copy the example environment file:

```bash
cp .env.example .env
```

Edit `.env` with your configuration:

```env
# Discord Configuration (Required)
DISCORD_BOT_TOKEN=your_discord_bot_token_here
DISCORD_APPLICATION_ID=your_discord_application_id_here

# Security (Required)
JWT_SECRET=your_jwt_secret_key_here_minimum_32_characters
API_KEYS=your_api_key_1:tenant_1,your_api_key_2:tenant_2
ENCRYPTION_KEY=your_32_character_encryption_key_here

# Server Configuration
MCP_SERVER_PORT=3000
MCP_INSPECTOR_PORT=3001
LOG_LEVEL=info

# Database
DATABASE_URL=sqlite:./data/discord_mcp.db

# Rate Limiting
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=60000
```

### 3. Discord Bot Setup

1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Create a new application
3. Go to "Bot" section and create a bot
4. Copy the bot token to your `.env` file
5. Enable required intents:
   - Message Content Intent
   - Server Members Intent
   - Guild Messages Intent
6. Generate invite URL with required permissions:
   - View Channels
   - Send Messages
   - Read Message History
   - Manage Messages
   - Embed Links
   - Kick Members
   - Ban Members
   - Moderate Members

## Development Deployment

### Local Development

```bash
# Install dependencies
npm install

# Run in development mode
npm run dev

# Run tests
npm test

# Run with inspector
npm run inspector
```

### Environment Validation

```bash
# Validate environment setup
npm run validate-env

# Generate environment report
npm run env-report
```

## Production Deployment

### 1. Build for Production

```bash
# Install production dependencies
npm ci --only=production

# Build TypeScript
npm run build

# Run production server
npm start
```

### 2. Production Environment Variables

```env
NODE_ENV=production
LOG_LEVEL=warn
MCP_INSPECTOR_ENABLED=false
DATABASE_URL=postgresql://user:password@host:port/database
```

### 3. Database Setup (PostgreSQL)

```sql
-- Create database
CREATE DATABASE discord_mcp;

-- Create user
CREATE USER mcp_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE discord_mcp TO mcp_user;
```

Update `.env`:
```env
DATABASE_URL=postgresql://mcp_user:secure_password@localhost:5432/discord_mcp
```

### 4. Process Management

#### Using PM2

```bash
# Install PM2
npm install -g pm2

# Start with PM2
pm2 start dist/index.js --name discord-mcp-server

# Save PM2 configuration
pm2 save

# Setup startup script
pm2 startup
```

#### Using systemd

Create `/etc/systemd/system/discord-mcp-server.service`:

```ini
[Unit]
Description=Discord MCP Server
After=network.target

[Service]
Type=simple
User=mcp
WorkingDirectory=/opt/discord-mcp-server
ExecStart=/usr/bin/node dist/index.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
EnvironmentFile=/opt/discord-mcp-server/.env

[Install]
WantedBy=multi-user.target
```

Enable and start:
```bash
sudo systemctl enable discord-mcp-server
sudo systemctl start discord-mcp-server
sudo systemctl status discord-mcp-server
```

## Cloud Deployment

### Railway

1. Connect your GitHub repository to Railway
2. Set environment variables in Railway dashboard
3. Deploy automatically on push

### Heroku

```bash
# Install Heroku CLI
# Login to Heroku
heroku login

# Create app
heroku create your-discord-mcp-server

# Set environment variables
heroku config:set DISCORD_BOT_TOKEN=your_token
heroku config:set DISCORD_APPLICATION_ID=your_app_id
heroku config:set JWT_SECRET=your_jwt_secret
heroku config:set API_KEYS=your_api_keys
heroku config:set ENCRYPTION_KEY=your_encryption_key

# Add PostgreSQL addon
heroku addons:create heroku-postgresql:hobby-dev

# Deploy
git push heroku main
```

### AWS EC2

1. Launch EC2 instance (Ubuntu 20.04+)
2. Install Node.js and dependencies
3. Clone repository and configure
4. Setup nginx reverse proxy
5. Configure SSL with Let's Encrypt

#### Nginx Configuration

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    location /inspector {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## Security Considerations

### Production Security Checklist

- [ ] Use strong, unique API keys (32+ characters)
- [ ] Set `NODE_ENV=production`
- [ ] Disable MCP Inspector in production
- [ ] Use PostgreSQL instead of SQLite
- [ ] Enable HTTPS/SSL
- [ ] Restrict CORS origins
- [ ] Use environment variables for secrets
- [ ] Enable audit logging
- [ ] Set up log rotation
- [ ] Configure firewall rules
- [ ] Regular security updates

### Environment Security

```bash
# Set proper file permissions
chmod 600 .env
chmod 700 data/
chmod 700 logs/

# Create dedicated user
sudo useradd -r -s /bin/false mcp
sudo chown -R mcp:mcp /opt/discord-mcp-server
```

## Monitoring and Maintenance

### Health Checks

```bash
# Check server health
curl http://localhost:3000/health

# Check inspector (if enabled)
curl http://localhost:3001/api/status
```

### Log Management

```bash
# View logs
tail -f logs/discord-mcp-server.log

# View error logs
tail -f logs/discord-mcp-server.error.log

# View audit logs
tail -f logs/discord-mcp-server.audit.log
```

### Database Maintenance

```bash
# Backup SQLite database
cp data/discord_mcp.db data/discord_mcp.db.backup

# Backup PostgreSQL database
pg_dump discord_mcp > backup.sql
```

## Troubleshooting

### Common Issues

1. **Bot not responding**
   - Check Discord bot token
   - Verify bot permissions
   - Check bot is online in Discord

2. **Authentication errors**
   - Verify API keys in environment
   - Check JWT secret configuration
   - Validate encryption key length

3. **Database connection errors**
   - Check database URL format
   - Verify database permissions
   - Ensure database server is running

4. **Rate limiting issues**
   - Check rate limit configuration
   - Monitor request patterns
   - Adjust limits if needed

### Debug Mode

```bash
# Enable debug logging
LOG_LEVEL=debug npm start

# Run with inspector
MCP_INSPECTOR_ENABLED=true npm start
```

### Performance Tuning

```bash
# Monitor memory usage
node --max-old-space-size=4096 dist/index.js

# Enable clustering
PM2_INSTANCES=max pm2 start dist/index.js
```

## Backup and Recovery

### Automated Backups

```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
cp data/discord_mcp.db backups/discord_mcp_$DATE.db
find backups/ -name "*.db" -mtime +7 -delete
```

### Recovery Procedures

1. Stop the server
2. Restore database from backup
3. Verify configuration
4. Restart server
5. Test functionality

## Support

- **Documentation**: `/docs` folder
- **Issues**: GitHub repository issues
- **Logs**: Check application logs for errors
- **Inspector**: Use MCP Inspector for debugging
