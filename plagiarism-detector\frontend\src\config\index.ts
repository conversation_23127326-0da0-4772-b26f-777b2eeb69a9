// Frontend configuration using Vite environment variables

interface AppConfig {
  // API Configuration
  apiBaseUrl: string;
  apiTimeout: number;

  // Application Configuration
  appName: string;
  appVersion: string;
  appDescription: string;

  // Feature Flags
  enable3DVisualization: boolean;
  enableModelComparison: boolean;
  enableAdvancedAnalytics: boolean;
  enableExportFeatures: boolean;

  // UI Configuration
  defaultTheme: 'light' | 'dark';
  enableDarkMode: boolean;
  animationDuration: number;
  enableSoundEffects: boolean;

  // Similarity Detection UI
  defaultSimilarityThreshold: number;
  maxTextInputs: number;
  maxTextLength: number;
  showSimilarityPercentages: boolean;

  // Development
  debugMode: boolean;
  enableDevtools: boolean;
  logLevel: 'error' | 'warn' | 'info' | 'debug';

  // Analytics
  enableAnalytics: boolean;
  analyticsId?: string;

  // Performance
  enableServiceWorker: boolean;
  cacheDuration: number;
}

// Helper function to parse boolean environment variables
const parseBoolean = (value: string | undefined, defaultValue: boolean = false): boolean => {
  if (value === undefined) return defaultValue;
  return value.toLowerCase() === 'true';
};

// Helper function to parse number environment variables
const parseNumber = (value: string | undefined, defaultValue: number): number => {
  if (value === undefined) return defaultValue;
  const parsed = Number(value);
  return isNaN(parsed) ? defaultValue : parsed;
};

// Create configuration object
export const config: AppConfig = {
  // API Configuration
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001',
  apiTimeout: parseNumber(import.meta.env.VITE_API_TIMEOUT, 30000),

  // Application Configuration
  appName: import.meta.env.VITE_APP_NAME || 'Plagiarism Detector',
  appVersion: import.meta.env.VITE_APP_VERSION || '1.0.0',
  appDescription: import.meta.env.VITE_APP_DESCRIPTION || 'Semantic Similarity Analyzer',

  // Feature Flags
  enable3DVisualization: parseBoolean(import.meta.env.VITE_ENABLE_3D_VISUALIZATION, true),
  enableModelComparison: parseBoolean(import.meta.env.VITE_ENABLE_MODEL_COMPARISON, true),
  enableAdvancedAnalytics: parseBoolean(import.meta.env.VITE_ENABLE_ADVANCED_ANALYTICS, true),
  enableExportFeatures: parseBoolean(import.meta.env.VITE_ENABLE_EXPORT_FEATURES, true),

  // UI Configuration
  defaultTheme: (import.meta.env.VITE_DEFAULT_THEME as 'light' | 'dark') || 'light',
  enableDarkMode: parseBoolean(import.meta.env.VITE_ENABLE_DARK_MODE, true),
  animationDuration: parseNumber(import.meta.env.VITE_ANIMATION_DURATION, 300),
  enableSoundEffects: parseBoolean(import.meta.env.VITE_ENABLE_SOUND_EFFECTS, false),

  // Similarity Detection UI
  defaultSimilarityThreshold: parseNumber(import.meta.env.VITE_DEFAULT_SIMILARITY_THRESHOLD, 80),
  maxTextInputs: parseNumber(import.meta.env.VITE_MAX_TEXT_INPUTS, 10),
  maxTextLength: parseNumber(import.meta.env.VITE_MAX_TEXT_LENGTH, 10000),
  showSimilarityPercentages: parseBoolean(import.meta.env.VITE_SHOW_SIMILARITY_PERCENTAGES, true),

  // Development
  debugMode: parseBoolean(import.meta.env.VITE_DEBUG_MODE, false),
  enableDevtools: parseBoolean(import.meta.env.VITE_ENABLE_DEVTOOLS, true),
  logLevel: (import.meta.env.VITE_LOG_LEVEL as 'error' | 'warn' | 'info' | 'debug') || 'info',

  // Analytics
  enableAnalytics: parseBoolean(import.meta.env.VITE_ENABLE_ANALYTICS, false),
  analyticsId: import.meta.env.VITE_ANALYTICS_ID,

  // Performance
  enableServiceWorker: parseBoolean(import.meta.env.VITE_ENABLE_SERVICE_WORKER, false),
  cacheDuration: parseNumber(import.meta.env.VITE_CACHE_DURATION, 300000),
};

// API endpoints configuration
export const apiEndpoints = {
  // Text processing
  preprocessText: '/api/text/preprocess',
  
  // Embedding generation
  generateEmbeddings: '/api/embeddings/generate',
  compareModels: '/api/embeddings/compare-models',
  
  // Similarity calculation
  calculateSimilarity: '/api/similarity/calculate',
  detectClones: '/api/similarity/detect-clones',
  
  // Analysis
  analyzeTexts: '/api/analysis/analyze',
  generateReport: '/api/analysis/report',
  
  // Health check
  health: '/api/health',
  status: '/api/status',
} as const;

// Validation thresholds
export const validationRules = {
  minTextLength: 10,
  maxTextLength: config.maxTextLength,
  minTexts: 2,
  maxTexts: config.maxTextInputs,
  minSimilarityThreshold: 0,
  maxSimilarityThreshold: 100,
} as const;

// UI constants
export const uiConstants = {
  // Animation durations
  fastAnimation: config.animationDuration / 2,
  normalAnimation: config.animationDuration,
  slowAnimation: config.animationDuration * 2,
  
  // Colors for similarity levels
  similarityColors: {
    low: '#10B981',      // Green
    medium: '#F59E0B',   // Yellow
    high: '#EF4444',     // Red
    clone: '#DC2626',    // Dark Red
  },
  
  // Similarity thresholds for color coding
  similarityThresholds: {
    low: 30,
    medium: 60,
    high: 80,
    clone: 90,
  },
  
  // Chart dimensions
  chartDimensions: {
    minHeight: 400,
    maxHeight: 800,
    aspectRatio: 1.2,
  },
} as const;

// Development helpers
export const isDevelopment = import.meta.env.DEV;
export const isProduction = import.meta.env.PROD;

// Configuration validation
export const validateConfig = (): void => {
  const requiredFields = ['apiBaseUrl', 'appName'] as const;
  
  for (const field of requiredFields) {
    if (!config[field]) {
      throw new Error(`Missing required configuration: ${field}`);
    }
  }
  
  if (config.debugMode) {
    console.log('🔧 Frontend configuration:', config);
  }
  
  console.log(`✅ ${config.appName} v${config.appVersion} configuration loaded`);
  console.log(`🌐 API Base URL: ${config.apiBaseUrl}`);
  console.log(`🎨 Theme: ${config.defaultTheme}`);
  console.log(`📊 3D Visualization: ${config.enable3DVisualization ? 'enabled' : 'disabled'}`);
};
