import natural from 'natural';
import compromise from 'compromise';
import { TextInput, ProcessedText, PreprocessingOptions } from '../types/index.js';
// Remove unused import

/**
 * Text Preprocessing Service
 * Handles text cleaning, normalization, and preparation for embedding generation
 */
export class TextPreprocessorService {
  private stemmer = natural.PorterStemmer;
  private tokenizer = new natural.WordTokenizer();
  private stopWords = new Set(natural.stopwords);

  /**
   * Process a single text input with specified options
   */
  async processText(
    textInput: TextInput,
    options: PreprocessingOptions
  ): Promise<ProcessedText> {
    const startTime = Date.now();
    const processingSteps: string[] = [];
    
    let processedContent = textInput.content;
    const originalLength = processedContent.length;

    try {
      // Step 1: Basic cleaning
      if (options.removeExtraWhitespace) {
        processedContent = this.removeExtraWhitespace(processedContent);
        processingSteps.push('removed_extra_whitespace');
      }

      // Step 2: Case normalization
      if (options.toLowerCase) {
        processedContent = processedContent.toLowerCase();
        processingSteps.push('converted_to_lowercase');
      }

      // Step 3: Remove numbers
      if (options.removeNumbers) {
        processedContent = this.removeNumbers(processedContent);
        processingSteps.push('removed_numbers');
      }

      // Step 4: Remove punctuation
      if (options.removePunctuation) {
        processedContent = this.removePunctuation(processedContent);
        processingSteps.push('removed_punctuation');
      }

      // Step 5: Tokenization and filtering
      let tokens = this.tokenizer.tokenize(processedContent) || [];
      
      // Filter by minimum word length
      if (options.minWordLength > 0) {
        tokens = tokens.filter(token => token.length >= options.minWordLength);
        processingSteps.push(`filtered_min_length_${options.minWordLength}`);
      }

      // Step 6: Remove stop words
      if (options.removeStopWords) {
        tokens = this.removeStopWords(tokens, options.customStopWords);
        processingSteps.push('removed_stop_words');
      }

      // Step 7: Stemming
      if (options.stemming && !options.lemmatization) {
        tokens = this.applyStemming(tokens);
        processingSteps.push('applied_stemming');
      }

      // Step 8: Lemmatization (preferred over stemming if both are enabled)
      if (options.lemmatization) {
        tokens = await this.applyLemmatization(tokens);
        processingSteps.push('applied_lemmatization');
      }

      // Reconstruct processed text
      processedContent = tokens.join(' ');

      // Calculate statistics
      const wordCount = tokens.length;
      const sentenceCount = this.countSentences(textInput.content);
      const processedLength = processedContent.length;

      const processedText: ProcessedText = {
        id: textInput.id,
        originalContent: textInput.content,
        processedContent,
        label: textInput.label,
        metadata: {
          originalLength,
          processedLength,
          wordCount,
          sentenceCount,
          languageDetected: this.detectLanguage(textInput.content),
          processingSteps,
          timestamp: new Date(),
        },
      };

      return processedText;
    } catch (error) {
      throw new Error(`Text preprocessing failed for text ${textInput.id}: ${error}`);
    }
  }

  /**
   * Process multiple texts in batch
   */
  async processTexts(
    textInputs: TextInput[],
    options: PreprocessingOptions
  ): Promise<ProcessedText[]> {
    const results: ProcessedText[] = [];
    
    for (const textInput of textInputs) {
      try {
        const processed = await this.processText(textInput, options);
        results.push(processed);
      } catch (error) {
        console.error(`Failed to process text ${textInput.id}:`, error);
        // Continue processing other texts even if one fails
        results.push({
          id: textInput.id,
          originalContent: textInput.content,
          processedContent: textInput.content, // Fallback to original
          label: textInput.label,
          metadata: {
            originalLength: textInput.content.length,
            processedLength: textInput.content.length,
            wordCount: this.tokenizer.tokenize(textInput.content)?.length || 0,
            sentenceCount: this.countSentences(textInput.content),
            processingSteps: ['preprocessing_failed'],
            timestamp: new Date(),
          },
        });
      }
    }

    return results;
  }

  /**
   * Remove extra whitespace and normalize spacing
   */
  private removeExtraWhitespace(text: string): string {
    return text
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .replace(/\n\s*\n/g, '\n') // Replace multiple newlines with single newline
      .trim();
  }

  /**
   * Remove numbers from text
   */
  private removeNumbers(text: string): string {
    return text.replace(/\d+/g, '');
  }

  /**
   * Remove punctuation while preserving sentence structure
   */
  private removePunctuation(text: string): string {
    // Keep sentence-ending punctuation for sentence counting
    return text.replace(/[^\w\s.!?]/g, '');
  }

  /**
   * Remove stop words from token array
   */
  private removeStopWords(tokens: string[], customStopWords?: string[]): string[] {
    const allStopWords = new Set([...this.stopWords]);
    
    if (customStopWords) {
      customStopWords.forEach(word => allStopWords.add(word.toLowerCase()));
    }

    return tokens.filter(token => !allStopWords.has(token.toLowerCase()));
  }

  /**
   * Apply stemming to tokens
   */
  private applyStemming(tokens: string[]): string[] {
    return tokens.map(token => this.stemmer.stem(token));
  }

  /**
   * Apply lemmatization using compromise library
   */
  private async applyLemmatization(tokens: string[]): Promise<string[]> {
    const text = tokens.join(' ');
    const doc = compromise(text);
    
    // Get lemmatized forms
    const lemmatized = doc.terms().out('root');
    
    return lemmatized.split(' ').filter(token => token.length > 0);
  }

  /**
   * Count sentences in text
   */
  private countSentences(text: string): number {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    return sentences.length;
  }

  /**
   * Detect language of text (basic implementation)
   */
  private detectLanguage(text: string): string {
    // Simple language detection based on common words
    // In production, you might want to use a proper language detection library
    const englishWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
    const words = text.toLowerCase().split(/\s+/);
    const englishWordCount = words.filter(word => englishWords.includes(word)).length;
    const englishRatio = englishWordCount / words.length;
    
    return englishRatio > 0.1 ? 'en' : 'unknown';
  }

  /**
   * Validate text input
   */
  validateTextInput(textInput: TextInput, maxLength: number): void {
    if (!textInput.content || textInput.content.trim().length === 0) {
      throw new Error(`Text content is empty for input ${textInput.id}`);
    }

    if (textInput.content.length > maxLength) {
      throw new Error(`Text content exceeds maximum length of ${maxLength} characters`);
    }

    if (textInput.content.length < 10) {
      throw new Error(`Text content is too short (minimum 10 characters required)`);
    }
  }

  /**
   * Get default preprocessing options
   */
  static getDefaultOptions(): PreprocessingOptions {
    return {
      removeStopWords: true,
      stemming: false,
      lemmatization: true,
      removeNumbers: false,
      removePunctuation: false,
      toLowerCase: true,
      removeExtraWhitespace: true,
      minWordLength: 2,
    };
  }

  /**
   * Get preprocessing options optimized for plagiarism detection
   */
  static getPlagiarismDetectionOptions(): PreprocessingOptions {
    return {
      removeStopWords: false, // Keep stop words for better semantic similarity
      stemming: false,
      lemmatization: true,
      removeNumbers: false, // Numbers might be important for plagiarism
      removePunctuation: false, // Punctuation can indicate structure
      toLowerCase: true,
      removeExtraWhitespace: true,
      minWordLength: 1, // Keep all words
    };
  }
}
