import { Client, GatewayIntentBits, Events } from 'discord.js';
import { discordConfig } from '../config/index.js';
import { loggers } from '../utils/logger.js';
import { DiscordAPIError } from '../types/index.js';

/**
 * Discord Client Manager
 * Handles Discord bot connections and multi-tenancy
 */
export class DiscordClient {
  private clients: Map<string, Client> = new Map();
  private clientConfigs: Map<string, { token: string; ready: boolean }> = new Map();

  constructor() {
    // Initialize default client configuration
    this.clientConfigs.set('default', {
      token: discordConfig.botToken,
      ready: false
    });
  }

  /**
   * Get or create a Discord client for a specific tenant
   */
  async getClient(tenantId: string): Promise<Client> {
    let client = this.clients.get(tenantId);
    
    if (!client) {
      client = await this.createClient(tenantId);
      this.clients.set(tenantId, client);
    }

    // Ensure client is ready
    if (!this.clientConfigs.get(tenantId)?.ready) {
      await this.waitForReady(client, tenantId);
    }

    return client;
  }

  /**
   * Create a new Discord client for a tenant
   */
  private async createClient(tenantId: string): Promise<Client> {
    const config = this.clientConfigs.get(tenantId);
    if (!config) {
      throw new DiscordAPIError(`No configuration found for tenant: ${tenantId}`);
    }

    loggers.info('Creating Discord client', { tenantId });

    const client = new Client({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildMembers
      ]
    });

    // Set up event handlers
    this.setupEventHandlers(client, tenantId);

    try {
      await client.login(config.token);
      loggers.info('Discord client logged in successfully', { tenantId });
    } catch (error) {
      loggers.error('Failed to login Discord client', error as Error, { tenantId });
      throw new DiscordAPIError('Failed to authenticate with Discord API');
    }

    return client;
  }

  /**
   * Setup event handlers for Discord client
   */
  private setupEventHandlers(client: Client, tenantId: string): void {
    client.once(Events.ClientReady, (readyClient) => {
      const config = this.clientConfigs.get(tenantId);
      if (config) {
        config.ready = true;
      }
      
      loggers.info('Discord client ready', {
        tenantId,
        username: readyClient.user.tag,
        guilds: readyClient.guilds.cache.size
      });
    });

    client.on(Events.Error, (error) => {
      loggers.error('Discord client error', error, { tenantId });
    });

    client.on(Events.Warn, (warning) => {
      loggers.warn('Discord client warning', { tenantId, warning });
    });

    client.on(Events.Debug, (debug) => {
      loggers.debug('Discord client debug', { tenantId, debug });
    });

    // Rate limit handling
    client.rest.on('rateLimited', (rateLimitData) => {
      loggers.warn('Discord API rate limited', {
        tenantId,
        timeout: rateLimitData.timeout,
        limit: rateLimitData.limit,
        method: rateLimitData.method,
        url: rateLimitData.url
      });
    });

    // API response logging
    client.rest.on('response', (request, response) => {
      loggers.discordResponse(
        request.path,
        response.status,
        Date.now() - request.timestamp
      );
    });
  }

  /**
   * Wait for client to be ready
   */
  private async waitForReady(client: Client, tenantId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new DiscordAPIError(`Client ready timeout for tenant: ${tenantId}`));
      }, 30000); // 30 second timeout

      const checkReady = () => {
        if (this.clientConfigs.get(tenantId)?.ready) {
          clearTimeout(timeout);
          resolve();
        } else {
          setTimeout(checkReady, 100);
        }
      };

      checkReady();
    });
  }

  /**
   * Add a new tenant configuration
   */
  addTenant(tenantId: string, botToken: string): void {
    this.clientConfigs.set(tenantId, {
      token: botToken,
      ready: false
    });

    loggers.info('Added new tenant configuration', { tenantId });
  }

  /**
   * Test Discord bot connection
   */
  async testConnection(tenantId: string): Promise<{ success: boolean; error?: string; botInfo?: any }> {
    try {
      const client = await this.getClient(tenantId);

      if (!client.user) {
        return { success: false, error: 'Bot user not available' };
      }

      const botInfo = {
        id: client.user.id,
        username: client.user.username,
        discriminator: client.user.discriminator,
        avatar: client.user.avatarURL(),
        verified: client.user.verified,
        guilds: client.guilds.cache.size,
        uptime: client.uptime
      };

      loggers.info('Discord bot connection test successful', { tenantId, botInfo });

      return { success: true, botInfo };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      loggers.error('Discord bot connection test failed', error as Error, { tenantId });

      return { success: false, error: errorMessage };
    }
  }

  /**
   * Get bot permissions in a specific guild
   */
  async getBotPermissions(tenantId: string, guildId: string): Promise<string[]> {
    try {
      const client = await this.getClient(tenantId);
      const guild = client.guilds.cache.get(guildId);

      if (!guild) {
        throw new DiscordAPIError(`Guild not found: ${guildId}`);
      }

      const botMember = guild.members.me;
      if (!botMember) {
        throw new DiscordAPIError('Bot is not a member of this guild');
      }

      const permissions = botMember.permissions.toArray();

      loggers.debug('Retrieved bot permissions', {
        tenantId,
        guildId,
        permissions: permissions.length
      });

      return permissions;

    } catch (error) {
      loggers.error('Failed to get bot permissions', error as Error, { tenantId, guildId });
      throw error;
    }
  }

  /**
   * Get bot's accessible channels in a guild
   */
  async getAccessibleChannels(tenantId: string, guildId: string): Promise<any[]> {
    try {
      const client = await this.getClient(tenantId);
      const guild = client.guilds.cache.get(guildId);

      if (!guild) {
        throw new DiscordAPIError(`Guild not found: ${guildId}`);
      }

      const botMember = guild.members.me;
      if (!botMember) {
        throw new DiscordAPIError('Bot is not a member of this guild');
      }

      const accessibleChannels = guild.channels.cache
        .filter(channel => {
          const permissions = channel.permissionsFor(botMember);
          return permissions?.has('ViewChannel') && permissions?.has('ReadMessageHistory');
        })
        .map(channel => ({
          id: channel.id,
          name: channel.name,
          type: channel.type,
          permissions: channel.permissionsFor(botMember)?.toArray() || []
        }));

      loggers.debug('Retrieved accessible channels', {
        tenantId,
        guildId,
        channelCount: accessibleChannels.length
      });

      return accessibleChannels;

    } catch (error) {
      loggers.error('Failed to get accessible channels', error as Error, { tenantId, guildId });
      throw error;
    }
  }

  /**
   * Remove a tenant and cleanup its client
   */
  async removeTenant(tenantId: string): Promise<void> {
    const client = this.clients.get(tenantId);
    if (client) {
      await client.destroy();
      this.clients.delete(tenantId);
    }
    
    this.clientConfigs.delete(tenantId);
    loggers.info('Removed tenant configuration', { tenantId });
  }

  /**
   * Get client status for a tenant
   */
  getClientStatus(tenantId: string): { exists: boolean; ready: boolean; guilds?: number } {
    const client = this.clients.get(tenantId);
    const config = this.clientConfigs.get(tenantId);
    
    return {
      exists: !!client,
      ready: config?.ready ?? false,
      guilds: client?.guilds.cache.size
    };
  }

  /**
   * Cleanup all clients
   */
  async cleanup(): Promise<void> {
    loggers.info('Cleaning up Discord clients');
    
    const cleanupPromises = Array.from(this.clients.values()).map(client => 
      client.destroy()
    );
    
    await Promise.all(cleanupPromises);
    
    this.clients.clear();
    this.clientConfigs.clear();
    
    loggers.info('Discord clients cleanup completed');
  }

  /**
   * Get all tenant IDs
   */
  getTenantIds(): string[] {
    return Array.from(this.clientConfigs.keys());
  }

  /**
   * Check if a tenant exists
   */
  hasTenant(tenantId: string): boolean {
    return this.clientConfigs.has(tenantId);
  }
}
