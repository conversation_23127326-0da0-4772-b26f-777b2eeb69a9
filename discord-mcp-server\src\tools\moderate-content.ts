import { Client, TextChannel, DMChannel, NewsChannel, ThreadChannel, GuildMember } from 'discord.js';
import { z } from 'zod';
import { loggers } from '../utils/logger.js';
import { DiscordAPIError, ValidationError } from '../types/index.js';

// Input schema for moderate_content tool
const ModerateContentInput = z.object({
  action: z.enum(['delete_message', 'timeout_user', 'ban_user', 'kick_user']),
  target_id: z.string().min(1, "Target ID is required"),
  reason: z.string().optional(),
  duration: z.number().min(60).max(2419200).optional() // 1 minute to 28 days
});

type ModerateContentArgs = z.infer<typeof ModerateContentInput>;

/**
 * Perform moderation actions on Discord content or users
 */
export async function moderateContent(client: Client, args: ModerateContentArgs) {
  const startTime = Date.now();
  
  try {
    loggers.info('Performing Discord moderation action', {
      action: args.action,
      targetId: args.target_id,
      reason: args.reason,
      duration: args.duration
    });

    let result;

    switch (args.action) {
      case 'delete_message':
        result = await deleteMessage(client, args.target_id, args.reason);
        break;
        
      case 'timeout_user':
        if (!args.duration) {
          throw new ValidationError('Duration is required for timeout_user action');
        }
        result = await timeoutUser(client, args.target_id, args.duration, args.reason);
        break;
        
      case 'ban_user':
        result = await banUser(client, args.target_id, args.reason);
        break;
        
      case 'kick_user':
        result = await kickUser(client, args.target_id, args.reason);
        break;
        
      default:
        throw new ValidationError(`Unknown moderation action: ${args.action}`);
    }

    const duration = Date.now() - startTime;
    
    loggers.audit('MODERATION_ACTION', {
      action: args.action,
      targetId: args.target_id,
      reason: args.reason,
      duration: args.duration,
      success: true,
      executionTime: duration
    });

    loggers.info('Discord moderation action completed', {
      action: args.action,
      targetId: args.target_id,
      duration
    });

    return {
      success: true,
      action: args.action,
      target_id: args.target_id,
      reason: args.reason,
      duration: args.duration,
      result
    };

  } catch (error) {
    const duration = Date.now() - startTime;
    
    loggers.audit('MODERATION_ACTION', {
      action: args.action,
      targetId: args.target_id,
      reason: args.reason,
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      executionTime: duration
    });

    loggers.error('Failed to perform Discord moderation action', error as Error, {
      action: args.action,
      targetId: args.target_id,
      duration
    });

    // Re-throw validation errors as-is
    if (error instanceof ValidationError) {
      throw error;
    }

    // Wrap other errors
    throw new DiscordAPIError(`Failed to perform moderation action: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Delete a message
 */
async function deleteMessage(client: Client, messageId: string, reason?: string) {
  try {
    // We need to find the message across all channels
    // This is a simplified approach - in production, you might want to cache channel-message relationships
    let message = null;
    let channel = null;

    // Try to find the message in all accessible channels
    for (const [, guild] of client.guilds.cache) {
      const textChannels = guild.channels.cache.filter(ch => 
        isTextBasedChannel(ch) && 
        ch.permissionsFor(guild.members.me!)?.has('ManageMessages')
      );
      
      for (const [, textChannel] of textChannels) {
        try {
          message = await textChannel.messages.fetch(messageId);
          channel = textChannel;
          break;
        } catch {
          // Message not in this channel, continue searching
        }
      }
      
      if (message) break;
    }

    if (!message || !channel) {
      throw new ValidationError(`Message not found or bot lacks permission: ${messageId}`);
    }

    // Store message info before deletion
    const messageInfo = {
      id: message.id,
      content: message.content,
      author: {
        id: message.author.id,
        username: message.author.username
      },
      channel: {
        id: channel.id,
        name: 'name' in channel ? channel.name : 'DM'
      },
      timestamp: message.createdAt.toISOString()
    };

    // Delete the message
    await message.delete();

    loggers.info('Message deleted successfully', {
      messageId,
      channelId: channel.id,
      authorId: message.author.id,
      reason
    });

    return {
      deleted_message: messageInfo,
      deleted_at: new Date().toISOString(),
      reason
    };

  } catch (error) {
    if (error instanceof Error) {
      if (error.message.includes('Missing Permissions')) {
        throw new DiscordAPIError('Bot lacks permission to delete messages in this channel');
      }
      
      if (error.message.includes('Unknown Message')) {
        throw new ValidationError(`Message not found: ${messageId}`);
      }
    }

    throw error;
  }
}

/**
 * Timeout a user
 */
async function timeoutUser(client: Client, userId: string, duration: number, reason?: string) {
  try {
    let member: GuildMember | null = null;
    let guild = null;

    // Find the user across all guilds
    for (const [, g] of client.guilds.cache) {
      try {
        member = await g.members.fetch(userId);
        guild = g;
        break;
      } catch {
        // User not in this guild, continue searching
      }
    }

    if (!member || !guild) {
      throw new ValidationError(`User not found in any accessible guild: ${userId}`);
    }

    // Check permissions
    if (!guild.members.me?.permissions.has('ModerateMembers')) {
      throw new DiscordAPIError('Bot lacks permission to timeout members');
    }

    // Calculate timeout end time
    const timeoutUntil = new Date(Date.now() + duration * 1000);

    // Apply timeout
    await member.timeout(duration * 1000, reason);

    loggers.info('User timed out successfully', {
      userId,
      guildId: guild.id,
      duration,
      timeoutUntil: timeoutUntil.toISOString(),
      reason
    });

    return {
      user: {
        id: member.id,
        username: member.user.username,
        nickname: member.nickname
      },
      guild: {
        id: guild.id,
        name: guild.name
      },
      timeout_duration: duration,
      timeout_until: timeoutUntil.toISOString(),
      reason
    };

  } catch (error) {
    if (error instanceof Error) {
      if (error.message.includes('Missing Permissions')) {
        throw new DiscordAPIError('Bot lacks permission to timeout members');
      }
      
      if (error.message.includes('Cannot timeout')) {
        throw new DiscordAPIError('Cannot timeout this user (may be bot owner or have higher role)');
      }
    }

    throw error;
  }
}

/**
 * Ban a user
 */
async function banUser(client: Client, userId: string, reason?: string) {
  try {
    let member: GuildMember | null = null;
    let guild = null;

    // Find the user across all guilds
    for (const [, g] of client.guilds.cache) {
      try {
        member = await g.members.fetch(userId);
        guild = g;
        break;
      } catch {
        // User not in this guild, continue searching
      }
    }

    if (!member || !guild) {
      throw new ValidationError(`User not found in any accessible guild: ${userId}`);
    }

    // Check permissions
    if (!guild.members.me?.permissions.has('BanMembers')) {
      throw new DiscordAPIError('Bot lacks permission to ban members');
    }

    // Store user info before ban
    const userInfo = {
      id: member.id,
      username: member.user.username,
      nickname: member.nickname,
      joined_at: member.joinedAt?.toISOString()
    };

    // Ban the user
    await member.ban({ reason, deleteMessageDays: 1 });

    loggers.info('User banned successfully', {
      userId,
      guildId: guild.id,
      reason
    });

    return {
      banned_user: userInfo,
      guild: {
        id: guild.id,
        name: guild.name
      },
      banned_at: new Date().toISOString(),
      reason
    };

  } catch (error) {
    if (error instanceof Error) {
      if (error.message.includes('Missing Permissions')) {
        throw new DiscordAPIError('Bot lacks permission to ban members');
      }
      
      if (error.message.includes('Cannot ban')) {
        throw new DiscordAPIError('Cannot ban this user (may be bot owner or have higher role)');
      }
    }

    throw error;
  }
}

/**
 * Kick a user
 */
async function kickUser(client: Client, userId: string, reason?: string) {
  try {
    let member: GuildMember | null = null;
    let guild = null;

    // Find the user across all guilds
    for (const [, g] of client.guilds.cache) {
      try {
        member = await g.members.fetch(userId);
        guild = g;
        break;
      } catch {
        // User not in this guild, continue searching
      }
    }

    if (!member || !guild) {
      throw new ValidationError(`User not found in any accessible guild: ${userId}`);
    }

    // Check permissions
    if (!guild.members.me?.permissions.has('KickMembers')) {
      throw new DiscordAPIError('Bot lacks permission to kick members');
    }

    // Store user info before kick
    const userInfo = {
      id: member.id,
      username: member.user.username,
      nickname: member.nickname,
      joined_at: member.joinedAt?.toISOString()
    };

    // Kick the user
    await member.kick(reason);

    loggers.info('User kicked successfully', {
      userId,
      guildId: guild.id,
      reason
    });

    return {
      kicked_user: userInfo,
      guild: {
        id: guild.id,
        name: guild.name
      },
      kicked_at: new Date().toISOString(),
      reason
    };

  } catch (error) {
    if (error instanceof Error) {
      if (error.message.includes('Missing Permissions')) {
        throw new DiscordAPIError('Bot lacks permission to kick members');
      }
      
      if (error.message.includes('Cannot kick')) {
        throw new DiscordAPIError('Cannot kick this user (may be bot owner or have higher role)');
      }
    }

    throw error;
  }
}

/**
 * Type guard to check if channel supports reading messages
 */
function isTextBasedChannel(channel: any): channel is TextChannel | DMChannel | NewsChannel | ThreadChannel {
  return channel && typeof channel.messages?.fetch === 'function';
}
