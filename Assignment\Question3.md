Q: 3
RAG Chunking Strategy Assignment
Task
Build a web application that allows users to upload PDF documents and visualize different chunking strategies for RAG systems.

Core Features
PDF Upload & Text Extraction - Extract and display text from uploaded PDFs
Chunking Strategy Dropdown - Explore and implement as many different strategies as you can
Strategy Explanation - Display how each selected strategy works
Chunk Visualization - Show the resulting chunks with metadata (size, overlap, etc.)
Technical Requirements
Use any framework: HTML/CSS/JS, React, Streamlit, or your choice
Handle PDFs of large sizes
Clean, documented code
Exploration Task
Research and implement different chunking approaches. Consider:

When to use each strategy
Trade-offs between chunk size and semantic coherence
How overlap affects retrieval performance
Deliverables
Working web application
Submit GitHub repository URL