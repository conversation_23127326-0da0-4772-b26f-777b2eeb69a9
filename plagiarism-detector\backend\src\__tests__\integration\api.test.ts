import request from 'supertest';
import app from '../../app';

describe('API Integration Tests', () => {
  describe('Health Check', () => {
    it('should return health status', async () => {
      const response = await request(app.app)
        .get('/health')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('status', 'healthy');
      expect(response.body.data).toHaveProperty('timestamp');
      expect(response.body.data).toHaveProperty('services');
    });
  });

  describe('Models Endpoint', () => {
    it('should return available models', async () => {
      const response = await request(app.app)
        .get('/api/models')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(Array.isArray(response.body.data)).toBe(true);
      
      if (response.body.data.length > 0) {
        const model = response.body.data[0];
        expect(model).toHaveProperty('name');
        expect(model).toHaveProperty('displayName');
        expect(model).toHaveProperty('provider');
        expect(model).toHaveProperty('dimensions');
        expect(model).toHaveProperty('description');
      }
    });
  });

  describe('Analysis Endpoint', () => {
    it('should reject requests with insufficient texts', async () => {
      const response = await request(app)
        .post('/api/analyze')
        .send({
          texts: [
            {
              content: 'Single text document',
              label: 'Document 1'
            }
          ],
          options: {
            models: ['test-model'],
            threshold: 0.8,
            enableCloneDetection: true,
            enableModelComparison: false
          }
        })
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('code', 'VALIDATION_ERROR');
    });

    it('should reject requests with invalid threshold', async () => {
      const response = await request(app)
        .post('/api/analyze')
        .send({
          texts: [
            {
              content: 'First text document for testing',
              label: 'Document 1'
            },
            {
              content: 'Second text document for testing',
              label: 'Document 2'
            }
          ],
          options: {
            models: ['test-model'],
            threshold: 1.5, // Invalid threshold > 1
            enableCloneDetection: true,
            enableModelComparison: false
          }
        })
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('error');
    });

    it('should reject requests with empty text content', async () => {
      const response = await request(app)
        .post('/api/analyze')
        .send({
          texts: [
            {
              content: '',
              label: 'Empty Document'
            },
            {
              content: 'Valid text document',
              label: 'Document 2'
            }
          ],
          options: {
            models: ['test-model'],
            threshold: 0.8,
            enableCloneDetection: true,
            enableModelComparison: false
          }
        })
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('error');
    });

    it('should reject requests with text that is too short', async () => {
      const response = await request(app)
        .post('/api/analyze')
        .send({
          texts: [
            {
              content: 'Hi',
              label: 'Too Short'
            },
            {
              content: 'This is a valid text document with sufficient length',
              label: 'Valid Document'
            }
          ],
          options: {
            models: ['test-model'],
            threshold: 0.8,
            enableCloneDetection: true,
            enableModelComparison: false
          }
        })
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('error');
    });

    it('should handle missing required fields', async () => {
      const response = await request(app)
        .post('/api/analyze')
        .send({
          // Missing texts field
          options: {
            models: ['test-model'],
            threshold: 0.8,
            enableCloneDetection: true,
            enableModelComparison: false
          }
        })
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('code', 'VALIDATION_ERROR');
    });

    it('should handle malformed JSON', async () => {
      const response = await request(app)
        .post('/api/analyze')
        .set('Content-Type', 'application/json')
        .send('{ invalid json }')
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('error');
    });

    // Note: This test would require actual API keys and models to be configured
    // In a real test environment, you might want to mock the embedding services
    it.skip('should successfully analyze valid texts', async () => {
      const response = await request(app)
        .post('/api/analyze')
        .send({
          texts: [
            {
              content: 'This is the first document to analyze for plagiarism detection.',
              label: 'Document 1'
            },
            {
              content: 'This is the second document that might contain similar content.',
              label: 'Document 2'
            }
          ],
          options: {
            models: ['all-MiniLM-L6-v2'], // Use local model for testing
            threshold: 0.8,
            enableCloneDetection: true,
            enableModelComparison: false,
            preprocessingOptions: {
              removeStopWords: false,
              stemming: false,
              lemmatization: true,
              removeNumbers: false,
              removePunctuation: false,
              toLowerCase: true,
              removeExtraWhitespace: true,
              minWordLength: 1
            }
          }
        })
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('requestId');
      expect(response.body.data).toHaveProperty('processedTexts');
      expect(response.body.data).toHaveProperty('embeddings');
      expect(response.body.data).toHaveProperty('similarityMatrices');
      expect(response.body.data).toHaveProperty('summary');
    });
  });

  describe('Error Handling', () => {
    it('should handle 404 for unknown endpoints', async () => {
      const response = await request(app)
        .get('/api/unknown-endpoint')
        .expect(404);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('code', 'NOT_FOUND');
    });

    it('should handle unsupported HTTP methods', async () => {
      const response = await request(app)
        .patch('/api/analyze')
        .expect(405);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('code', 'METHOD_NOT_ALLOWED');
    });
  });

  describe('Rate Limiting', () => {
    it('should apply rate limiting to analysis endpoint', async () => {
      // This test would need to be adjusted based on your rate limiting configuration
      // For now, we'll just verify the endpoint exists and responds appropriately
      const response = await request(app)
        .post('/api/analyze')
        .send({
          texts: [
            {
              content: 'Test document one',
              label: 'Doc 1'
            },
            {
              content: 'Test document two',
              label: 'Doc 2'
            }
          ],
          options: {
            models: ['test-model'],
            threshold: 0.8,
            enableCloneDetection: false,
            enableModelComparison: false
          }
        });

      // Should either succeed or fail with validation, but not with rate limiting initially
      expect([200, 400, 500]).toContain(response.status);
    });
  });

  describe('CORS', () => {
    it('should include CORS headers', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.headers).toHaveProperty('access-control-allow-origin');
    });

    it('should handle preflight requests', async () => {
      const response = await request(app)
        .options('/api/analyze')
        .set('Origin', 'http://localhost:3000')
        .set('Access-Control-Request-Method', 'POST')
        .set('Access-Control-Request-Headers', 'Content-Type')
        .expect(204);

      expect(response.headers).toHaveProperty('access-control-allow-methods');
      expect(response.headers).toHaveProperty('access-control-allow-headers');
    });
  });
});
