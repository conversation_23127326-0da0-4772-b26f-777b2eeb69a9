import request from 'supertest';
import app from '../../app';

describe('Simple API Integration Tests', () => {
  describe('Health Check', () => {
    it('should return health status', async () => {
      const response = await request(app.app)
        .get('/health');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success');
    });
  });

  describe('Models Endpoint', () => {
    it('should return available models', async () => {
      const response = await request(app.app)
        .get('/api/models');

      expect([200, 500]).toContain(response.status); // May fail if services not configured
      if (response.status === 200) {
        expect(response.body).toHaveProperty('success');
        expect(response.body).toHaveProperty('data');
      }
    });
  });

  describe('Analysis Endpoint', () => {
    it('should reject requests with insufficient texts', async () => {
      const response = await request(app.app)
        .post('/api/analyze')
        .send({
          texts: [
            {
              content: 'Single text document',
              label: 'Document 1'
            }
          ],
          options: {
            models: ['test-model'],
            threshold: 0.8,
            enableCloneDetection: true,
            enableModelComparison: false
          }
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('success', false);
    });

    it('should reject requests with invalid threshold', async () => {
      const response = await request(app.app)
        .post('/api/analyze')
        .send({
          texts: [
            {
              content: 'First text document for testing',
              label: 'Document 1'
            },
            {
              content: 'Second text document for testing',
              label: 'Document 2'
            }
          ],
          options: {
            models: ['test-model'],
            threshold: 1.5, // Invalid threshold > 1
            enableCloneDetection: true,
            enableModelComparison: false
          }
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('success', false);
    });

    it('should reject requests with empty text content', async () => {
      const response = await request(app.app)
        .post('/api/analyze')
        .send({
          texts: [
            {
              content: '',
              label: 'Empty Document'
            },
            {
              content: 'Valid text document',
              label: 'Document 2'
            }
          ],
          options: {
            models: ['test-model'],
            threshold: 0.8,
            enableCloneDetection: true,
            enableModelComparison: false
          }
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('success', false);
    });

    it('should handle missing required fields', async () => {
      const response = await request(app.app)
        .post('/api/analyze')
        .send({
          // Missing texts field
          options: {
            models: ['test-model'],
            threshold: 0.8,
            enableCloneDetection: true,
            enableModelComparison: false
          }
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('success', false);
    });
  });

  describe('Error Handling', () => {
    it('should handle 404 for unknown endpoints', async () => {
      const response = await request(app.app)
        .get('/api/unknown-endpoint');

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('success', false);
    });
  });

  describe('CORS', () => {
    it('should include CORS headers', async () => {
      const response = await request(app.app)
        .get('/health');

      expect(response.headers).toHaveProperty('access-control-allow-origin');
    });

    it('should handle preflight requests', async () => {
      const response = await request(app.app)
        .options('/api/analyze')
        .set('Origin', 'http://localhost:3000')
        .set('Access-Control-Request-Method', 'POST')
        .set('Access-Control-Request-Headers', 'Content-Type');

      expect([200, 204]).toContain(response.status);
    });
  });
});
