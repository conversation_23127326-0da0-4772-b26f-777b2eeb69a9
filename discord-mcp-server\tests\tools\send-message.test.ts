import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { sendMessage } from '../../src/tools/send-message.js';
import { testUtils } from '../setup.js';
import { ValidationError, DiscordAPIError } from '../../src/types/index.js';

describe('sendMessage Tool', () => {
  let mockClient: any;

  beforeEach(() => {
    mockClient = testUtils.createMockDiscordClient();
  });

  describe('Valid Inputs', () => {
    it('should send a simple text message successfully', async () => {
      const args = {
        channel_id: 'channel1',
        content: 'Hello, World!'
      };

      const result = await sendMessage(mockClient, args);

      expect(result.success).toBe(true);
      expect(result.message.content).toBe('Hello, World!');
      expect(result.message.channel_id).toBe('channel1');
      expect(result.message.id).toBe('message1');
    });

    it('should send a message with embed successfully', async () => {
      const args = {
        channel_id: 'channel1',
        content: 'Message with embed',
        embed: {
          title: 'Test Embed',
          description: 'This is a test embed',
          color: 0x00ff00,
          fields: [
            { name: 'Field 1', value: 'Value 1', inline: true },
            { name: 'Field 2', value: 'Value 2', inline: false }
          ]
        }
      };

      const result = await sendMessage(mockClient, args);

      expect(result.success).toBe(true);
      expect(result.embed).toBeDefined();
      expect(result.embed.title).toBe('Test Embed');
      expect(result.embed.fields).toHaveLength(2);
    });

    it('should handle maximum length message', async () => {
      const args = {
        channel_id: 'channel1',
        content: 'a'.repeat(2000) // Discord's max message length
      };

      const result = await sendMessage(mockClient, args);

      expect(result.success).toBe(true);
      expect(result.message.content).toHaveLength(2000);
    });
  });

  describe('Invalid Inputs', () => {
    it('should throw ValidationError for missing channel_id', async () => {
      const args = {
        channel_id: '',
        content: 'Hello, World!'
      };

      await expect(sendMessage(mockClient, args)).rejects.toThrow(ValidationError);
    });

    it('should throw ValidationError for empty content', async () => {
      const args = {
        channel_id: 'channel1',
        content: ''
      };

      await expect(sendMessage(mockClient, args)).rejects.toThrow(ValidationError);
    });

    it('should throw ValidationError for content too long', async () => {
      const args = {
        channel_id: 'channel1',
        content: 'a'.repeat(2001) // Exceeds Discord's limit
      };

      await expect(sendMessage(mockClient, args)).rejects.toThrow(ValidationError);
    });

    it('should throw ValidationError for invalid channel ID format', async () => {
      const args = {
        channel_id: 'invalid-id',
        content: 'Hello, World!'
      };

      await expect(sendMessage(mockClient, args)).rejects.toThrow(ValidationError);
    });
  });

  describe('Discord API Errors', () => {
    it('should handle channel not found error', async () => {
      mockClient.channels.fetch.mockRejectedValue(new Error('Unknown Channel'));

      const args = {
        channel_id: 'nonexistent-channel',
        content: 'Hello, World!'
      };

      await expect(sendMessage(mockClient, args)).rejects.toThrow(ValidationError);
    });

    it('should handle missing permissions error', async () => {
      const mockChannel = {
        id: 'channel1',
        send: jest.fn().mockRejectedValue(new Error('Missing Permissions'))
      };
      mockClient.channels.fetch.mockResolvedValue(mockChannel);

      const args = {
        channel_id: 'channel1',
        content: 'Hello, World!'
      };

      await expect(sendMessage(mockClient, args)).rejects.toThrow(DiscordAPIError);
    });

    it('should handle DM channel restrictions', async () => {
      const mockChannel = {
        id: 'dm-channel',
        send: jest.fn().mockRejectedValue(new Error('Cannot send messages to this user'))
      };
      mockClient.channels.fetch.mockResolvedValue(mockChannel);

      const args = {
        channel_id: 'dm-channel',
        content: 'Hello, World!'
      };

      await expect(sendMessage(mockClient, args)).rejects.toThrow(DiscordAPIError);
    });
  });

  describe('Embed Validation', () => {
    it('should validate embed title length', async () => {
      const args = {
        channel_id: 'channel1',
        content: 'Message with invalid embed',
        embed: {
          title: 'a'.repeat(257), // Exceeds Discord's 256 character limit
          description: 'Valid description'
        }
      };

      await expect(sendMessage(mockClient, args)).rejects.toThrow(ValidationError);
    });

    it('should validate embed description length', async () => {
      const args = {
        channel_id: 'channel1',
        content: 'Message with invalid embed',
        embed: {
          title: 'Valid title',
          description: 'a'.repeat(4097) // Exceeds Discord's 4096 character limit
        }
      };

      await expect(sendMessage(mockClient, args)).rejects.toThrow(ValidationError);
    });

    it('should validate embed fields count', async () => {
      const fields = Array.from({ length: 26 }, (_, i) => ({
        name: `Field ${i}`,
        value: `Value ${i}`
      }));

      const args = {
        channel_id: 'channel1',
        content: 'Message with too many fields',
        embed: {
          title: 'Valid title',
          fields
        }
      };

      await expect(sendMessage(mockClient, args)).rejects.toThrow(ValidationError);
    });

    it('should validate embed field name length', async () => {
      const args = {
        channel_id: 'channel1',
        content: 'Message with invalid field',
        embed: {
          title: 'Valid title',
          fields: [
            {
              name: 'a'.repeat(257), // Exceeds 256 character limit
              value: 'Valid value'
            }
          ]
        }
      };

      await expect(sendMessage(mockClient, args)).rejects.toThrow(ValidationError);
    });

    it('should validate embed field value length', async () => {
      const args = {
        channel_id: 'channel1',
        content: 'Message with invalid field',
        embed: {
          title: 'Valid title',
          fields: [
            {
              name: 'Valid name',
              value: 'a'.repeat(1025) // Exceeds 1024 character limit
            }
          ]
        }
      };

      await expect(sendMessage(mockClient, args)).rejects.toThrow(ValidationError);
    });

    it('should validate total embed length', async () => {
      const args = {
        channel_id: 'channel1',
        content: 'Message with oversized embed',
        embed: {
          title: 'a'.repeat(2000),
          description: 'a'.repeat(4000),
          fields: [
            { name: 'a'.repeat(256), value: 'a'.repeat(1024) }
          ]
        }
      };

      await expect(sendMessage(mockClient, args)).rejects.toThrow(ValidationError);
    });
  });

  describe('Response Format', () => {
    it('should return correct response structure', async () => {
      const args = {
        channel_id: 'channel1',
        content: 'Test message'
      };

      const result = await sendMessage(mockClient, args);

      expect(result).toHaveProperty('success', true);
      expect(result).toHaveProperty('message');
      expect(result.message).toHaveProperty('id');
      expect(result.message).toHaveProperty('channel_id');
      expect(result.message).toHaveProperty('content');
      expect(result.message).toHaveProperty('timestamp');
      expect(result.message).toHaveProperty('url');
      expect(result.message).toHaveProperty('author');
      expect(result.message.author).toHaveProperty('id');
      expect(result.message.author).toHaveProperty('username');
      expect(result.message.author).toHaveProperty('bot');
    });

    it('should include embed in response when provided', async () => {
      const embed = {
        title: 'Test Embed',
        description: 'Test Description',
        color: 0xff0000
      };

      const args = {
        channel_id: 'channel1',
        content: 'Message with embed',
        embed
      };

      const result = await sendMessage(mockClient, args);

      expect(result).toHaveProperty('embed');
      expect(result.embed).toEqual(embed);
    });

    it('should not include embed in response when not provided', async () => {
      const args = {
        channel_id: 'channel1',
        content: 'Message without embed'
      };

      const result = await sendMessage(mockClient, args);

      expect(result.embed).toBeUndefined();
    });
  });
});
