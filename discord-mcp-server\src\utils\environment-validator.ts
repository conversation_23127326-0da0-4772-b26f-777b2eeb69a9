import fs from 'fs';
import path from 'path';
import { loggers } from './logger.js';
import { DiscordBotSetup } from '../discord/setup.js';

/**
 * Environment and Security Validator
 * Validates the entire environment setup for security and compliance
 */
export class EnvironmentValidator {

  /**
   * Run comprehensive environment validation
   */
  static async validateEnvironment(): Promise<{ valid: boolean; errors: string[]; warnings: string[] }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    loggers.info('Starting comprehensive environment validation...');

    // 1. Validate required environment variables
    const envValidation = this.validateEnvironmentVariables();
    errors.push(...envValidation.errors);
    warnings.push(...envValidation.warnings);

    // 2. Validate Discord configuration
    const discordValidation = DiscordBotSetup.validateConfiguration();
    if (!discordValidation.valid) {
      errors.push(...discordValidation.errors);
    }

    // 3. Validate file permissions and security
    const fileValidation = await this.validateFilePermissions();
    errors.push(...fileValidation.errors);
    warnings.push(...fileValidation.warnings);

    // 4. Validate database setup
    const dbValidation = await this.validateDatabaseSetup();
    errors.push(...dbValidation.errors);
    warnings.push(...dbValidation.warnings);

    // 5. Validate security configuration
    const securityValidation = this.validateSecurityConfiguration();
    errors.push(...securityValidation.errors);
    warnings.push(...securityValidation.warnings);

    // 6. Validate network configuration
    const networkValidation = this.validateNetworkConfiguration();
    errors.push(...networkValidation.errors);
    warnings.push(...networkValidation.warnings);

    const valid = errors.length === 0;

    loggers.info('Environment validation completed', {
      valid,
      errorCount: errors.length,
      warningCount: warnings.length
    });

    return { valid, errors, warnings };
  }

  /**
   * Validate environment variables
   */
  private static validateEnvironmentVariables(): { errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    const requiredVars = [
      'DISCORD_BOT_TOKEN',
      'DISCORD_APPLICATION_ID',
      'JWT_SECRET',
      'API_KEYS',
      'ENCRYPTION_KEY'
    ];

    const optionalVars = [
      'DATABASE_URL',
      'LOG_LEVEL',
      'MCP_SERVER_PORT',
      'RATE_LIMIT_MAX_REQUESTS'
    ];

    // Check required variables
    for (const varName of requiredVars) {
      if (!process.env[varName]) {
        errors.push(`Required environment variable missing: ${varName}`);
      } else if (process.env[varName]!.trim().length === 0) {
        errors.push(`Required environment variable is empty: ${varName}`);
      }
    }

    // Check optional variables
    for (const varName of optionalVars) {
      if (!process.env[varName]) {
        warnings.push(`Optional environment variable not set: ${varName} (using default)`);
      }
    }

    // Validate specific formats
    if (process.env.DISCORD_BOT_TOKEN && !process.env.DISCORD_BOT_TOKEN.match(/^[A-Za-z0-9_-]{24}\.[A-Za-z0-9_-]{6}\.[A-Za-z0-9_-]{27}$/)) {
      errors.push('DISCORD_BOT_TOKEN format appears invalid');
    }

    if (process.env.DISCORD_APPLICATION_ID && !process.env.DISCORD_APPLICATION_ID.match(/^\d{17,19}$/)) {
      errors.push('DISCORD_APPLICATION_ID format appears invalid');
    }

    if (process.env.JWT_SECRET && process.env.JWT_SECRET.length < 32) {
      errors.push('JWT_SECRET must be at least 32 characters long');
    }

    if (process.env.ENCRYPTION_KEY && process.env.ENCRYPTION_KEY.length !== 32) {
      errors.push('ENCRYPTION_KEY must be exactly 32 characters long');
    }

    return { errors, warnings };
  }

  /**
   * Validate file permissions and security
   */
  private static async validateFilePermissions(): Promise<{ errors: string[]; warnings: string[] }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Check if .env file exists and has proper permissions
      const envPath = '.env';
      if (fs.existsSync(envPath)) {
        const stats = fs.statSync(envPath);
        
        // Check file permissions (should not be world-readable)
        if (process.platform !== 'win32') {
          const mode = stats.mode & parseInt('777', 8);
          if (mode & parseInt('044', 8)) {
            warnings.push('.env file is readable by group/others - consider restricting permissions');
          }
        }
      } else {
        warnings.push('.env file not found - using environment variables');
      }

      // Check data directory permissions
      const dataDir = 'data';
      if (!fs.existsSync(dataDir)) {
        try {
          fs.mkdirSync(dataDir, { recursive: true });
          loggers.info('Created data directory');
        } catch (error) {
          errors.push(`Cannot create data directory: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      // Check logs directory permissions
      const logsDir = 'logs';
      if (!fs.existsSync(logsDir)) {
        try {
          fs.mkdirSync(logsDir, { recursive: true });
          loggers.info('Created logs directory');
        } catch (error) {
          errors.push(`Cannot create logs directory: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

    } catch (error) {
      errors.push(`File permission validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return { errors, warnings };
  }

  /**
   * Validate database setup
   */
  private static async validateDatabaseSetup(): Promise<{ errors: string[]; warnings: string[] }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      const dbUrl = process.env.DATABASE_URL || 'sqlite:./data/discord_mcp.db';
      
      if (dbUrl.startsWith('sqlite:')) {
        const dbPath = dbUrl.replace('sqlite:', '');
        const dbDir = path.dirname(dbPath);
        
        // Check if database directory exists
        if (!fs.existsSync(dbDir)) {
          try {
            fs.mkdirSync(dbDir, { recursive: true });
            loggers.info('Created database directory', { path: dbDir });
          } catch (error) {
            errors.push(`Cannot create database directory: ${error instanceof Error ? error.message : 'Unknown error'}`);
          }
        }

        // Check write permissions
        try {
          fs.accessSync(dbDir, fs.constants.W_OK);
        } catch (error) {
          errors.push(`Database directory is not writable: ${dbDir}`);
        }

      } else if (dbUrl.startsWith('postgresql:') || dbUrl.startsWith('postgres:')) {
        warnings.push('PostgreSQL database detected - ensure connection is properly configured');
      } else {
        warnings.push(`Unknown database type in DATABASE_URL: ${dbUrl}`);
      }

    } catch (error) {
      errors.push(`Database validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return { errors, warnings };
  }

  /**
   * Validate security configuration
   */
  private static validateSecurityConfiguration(): { errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check if running in production
    const isProduction = process.env.NODE_ENV === 'production';

    if (isProduction) {
      // Production-specific security checks
      
      if (process.env.LOG_LEVEL === 'debug') {
        warnings.push('Debug logging enabled in production - consider using "info" or "warn"');
      }

      if (!process.env.CORS_ORIGIN || process.env.CORS_ORIGIN === '*') {
        warnings.push('CORS origin not restricted in production');
      }

      if (process.env.MCP_INSPECTOR_ENABLED === 'true') {
        warnings.push('MCP Inspector enabled in production - consider disabling for security');
      }

    } else {
      // Development-specific warnings
      if (!process.env.LOG_LEVEL) {
        warnings.push('LOG_LEVEL not set - using default');
      }
    }

    // Check API key security
    if (process.env.API_KEYS) {
      const apiKeys = process.env.API_KEYS.split(',');
      for (const keyPair of apiKeys) {
        const [key] = keyPair.split(':');
        if (key && key.length < 16) {
          warnings.push(`API key appears too short: ${key.substring(0, 4)}...`);
        }
      }
    }

    // Check rate limiting configuration
    const maxRequests = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100');
    if (maxRequests > 1000) {
      warnings.push('Rate limit is very high - consider lowering for better security');
    }

    return { errors, warnings };
  }

  /**
   * Validate network configuration
   */
  private static validateNetworkConfiguration(): { errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check port configuration
    const serverPort = parseInt(process.env.MCP_SERVER_PORT || '3000');
    const inspectorPort = parseInt(process.env.MCP_INSPECTOR_PORT || '3001');

    if (serverPort === inspectorPort) {
      errors.push('MCP server and inspector cannot use the same port');
    }

    if (serverPort < 1024 && process.getuid && process.getuid() !== 0) {
      warnings.push(`Port ${serverPort} requires root privileges on Unix systems`);
    }

    // Check CORS configuration
    const corsOrigin = process.env.CORS_ORIGIN;
    if (corsOrigin === '*') {
      warnings.push('CORS allows all origins - consider restricting for production');
    }

    return { errors, warnings };
  }

  /**
   * Generate environment report
   */
  static async generateEnvironmentReport(): Promise<string> {
    const validation = await this.validateEnvironment();
    
    let report = '# Discord MCP Server Environment Report\n\n';
    
    report += `**Validation Status:** ${validation.valid ? '✅ PASSED' : '❌ FAILED'}\n\n`;
    
    if (validation.errors.length > 0) {
      report += '## ❌ Errors (Must Fix)\n\n';
      for (const error of validation.errors) {
        report += `- ${error}\n`;
      }
      report += '\n';
    }
    
    if (validation.warnings.length > 0) {
      report += '## ⚠️ Warnings (Recommended)\n\n';
      for (const warning of validation.warnings) {
        report += `- ${warning}\n`;
      }
      report += '\n';
    }
    
    if (validation.valid) {
      report += '## ✅ Environment Ready\n\n';
      report += 'Your Discord MCP Server environment is properly configured and ready to run.\n\n';
    } else {
      report += '## 🔧 Next Steps\n\n';
      report += '1. Fix all errors listed above\n';
      report += '2. Address warnings for better security\n';
      report += '3. Run validation again\n\n';
    }
    
    report += '## 📋 Environment Summary\n\n';
    report += `- Node.js Version: ${process.version}\n`;
    report += `- Platform: ${process.platform}\n`;
    report += `- Environment: ${process.env.NODE_ENV || 'development'}\n`;
    report += `- Working Directory: ${process.cwd()}\n`;
    report += `- Timestamp: ${new Date().toISOString()}\n`;
    
    return report;
  }

  /**
   * Save environment report to file
   */
  static async saveEnvironmentReport(filePath: string = './environment-report.md'): Promise<void> {
    try {
      const report = await this.generateEnvironmentReport();
      fs.writeFileSync(filePath, report, 'utf8');
      loggers.info('Environment report saved', { filePath });
    } catch (error) {
      loggers.error('Failed to save environment report', error as Error);
      throw error;
    }
  }
}

export { EnvironmentValidator };
