import { SimilarityService } from '../../services/similarityService';
import { EmbeddingVector, SimilarityMatrix } from '../../types';

describe('SimilarityService', () => {
  let similarityService: SimilarityService;

  beforeEach(() => {
    similarityService = new SimilarityService();
  });

  describe('calculateCosineSimilarity', () => {
    it('should calculate cosine similarity correctly for identical vectors', () => {
      const vector1: EmbeddingVector = {
        textId: 'text1',
        model: 'test-model',
        vector: [1, 2, 3, 4],
        dimensions: 4,
        metadata: {}
      };

      const vector2: EmbeddingVector = {
        textId: 'text2',
        model: 'test-model',
        vector: [1, 2, 3, 4],
        dimensions: 4,
        metadata: {}
      };

      const similarity = similarityService.calculateCosineSimilarity(vector1, vector2);
      expect(similarity).toBeCloseTo(1.0, 5);
    });

    it('should calculate cosine similarity correctly for orthogonal vectors', () => {
      const vector1: EmbeddingVector = {
        textId: 'text1',
        model: 'test-model',
        vector: [1, 0, 0, 0],
        dimensions: 4,
        metadata: {}
      };

      const vector2: EmbeddingVector = {
        textId: 'text2',
        model: 'test-model',
        vector: [0, 1, 0, 0],
        dimensions: 4,
        metadata: {}
      };

      const similarity = similarityService.calculateCosineSimilarity(vector1, vector2);
      expect(similarity).toBeCloseTo(0.0, 5);
    });

    it('should calculate cosine similarity correctly for opposite vectors', () => {
      const vector1: EmbeddingVector = {
        textId: 'text1',
        model: 'test-model',
        vector: [1, 1, 1, 1],
        dimensions: 4,
        metadata: {}
      };

      const vector2: EmbeddingVector = {
        textId: 'text2',
        model: 'test-model',
        vector: [-1, -1, -1, -1],
        dimensions: 4,
        metadata: {}
      };

      const similarity = similarityService.calculateCosineSimilarity(vector1, vector2);
      expect(similarity).toBeCloseTo(-1.0, 5);
    });

    it('should handle zero vectors gracefully', () => {
      const vector1: EmbeddingVector = {
        textId: 'text1',
        model: 'test-model',
        vector: [0, 0, 0, 0],
        dimensions: 4,
        metadata: {}
      };

      const vector2: EmbeddingVector = {
        textId: 'text2',
        model: 'test-model',
        vector: [1, 2, 3, 4],
        dimensions: 4,
        metadata: {}
      };

      const similarity = similarityService.calculateCosineSimilarity(vector1, vector2);
      expect(similarity).toBe(0);
    });
  });

  describe('calculateEuclideanDistance', () => {
    it('should calculate Euclidean distance correctly for identical vectors', () => {
      const vector1: EmbeddingVector = {
        textId: 'text1',
        model: 'test-model',
        vector: [1, 2, 3],
        dimensions: 3,
        metadata: {}
      };

      const vector2: EmbeddingVector = {
        textId: 'text2',
        model: 'test-model',
        vector: [1, 2, 3],
        dimensions: 3,
        metadata: {}
      };

      const distance = similarityService.calculateEuclideanDistance(vector1, vector2);
      expect(distance).toBeCloseTo(0.0, 5);
    });

    it('should calculate Euclidean distance correctly', () => {
      const vector1: EmbeddingVector = {
        textId: 'text1',
        model: 'test-model',
        vector: [0, 0, 0],
        dimensions: 3,
        metadata: {}
      };

      const vector2: EmbeddingVector = {
        textId: 'text2',
        model: 'test-model',
        vector: [3, 4, 0],
        dimensions: 3,
        metadata: {}
      };

      const distance = similarityService.calculateEuclideanDistance(vector1, vector2);
      expect(distance).toBeCloseTo(5.0, 5); // sqrt(3^2 + 4^2) = 5
    });
  });

  describe('calculateManhattanDistance', () => {
    it('should calculate Manhattan distance correctly', () => {
      const vector1: EmbeddingVector = {
        textId: 'text1',
        model: 'test-model',
        vector: [1, 2, 3],
        dimensions: 3,
        metadata: {}
      };

      const vector2: EmbeddingVector = {
        textId: 'text2',
        model: 'test-model',
        vector: [4, 6, 8],
        dimensions: 3,
        metadata: {}
      };

      const distance = similarityService.calculateManhattanDistance(vector1, vector2);
      expect(distance).toBe(15); // |1-4| + |2-6| + |3-8| = 3 + 4 + 5 = 12
    });

    it('should calculate Manhattan distance for identical vectors', () => {
      const vector1: EmbeddingVector = {
        textId: 'text1',
        model: 'test-model',
        vector: [1, 2, 3],
        dimensions: 3,
        metadata: {}
      };

      const vector2: EmbeddingVector = {
        textId: 'text2',
        model: 'test-model',
        vector: [1, 2, 3],
        dimensions: 3,
        metadata: {}
      };

      const distance = similarityService.calculateManhattanDistance(vector1, vector2);
      expect(distance).toBe(0);
    });
  });

  describe('generateSimilarityMatrix', () => {
    it('should generate similarity matrix correctly', async () => {
      const embeddings: EmbeddingVector[] = [
        {
          textId: 'text1',
          model: 'test-model',
          vector: [1, 0, 0],
          dimensions: 3,
          metadata: {}
        },
        {
          textId: 'text2',
          model: 'test-model',
          vector: [0, 1, 0],
          dimensions: 3,
          metadata: {}
        },
        {
          textId: 'text3',
          model: 'test-model',
          vector: [1, 0, 0],
          dimensions: 3,
          metadata: {}
        }
      ];

      const matrix = await similarityService.generateSimilarityMatrix(
        embeddings,
        'cosine',
        0.8
      );

      expect(matrix.textIds).toEqual(['text1', 'text2', 'text3']);
      expect(matrix.matrix).toHaveLength(3);
      expect(matrix.matrix[0]).toHaveLength(3);
      
      // Diagonal should be 1.0 (self-similarity)
      expect(matrix.matrix[0][0]).toBeCloseTo(1.0, 5);
      expect(matrix.matrix[1][1]).toBeCloseTo(1.0, 5);
      expect(matrix.matrix[2][2]).toBeCloseTo(1.0, 5);
      
      // text1 and text3 should be identical (similarity = 1.0)
      expect(matrix.matrix[0][2]).toBeCloseTo(1.0, 5);
      expect(matrix.matrix[2][0]).toBeCloseTo(1.0, 5);
      
      // text1 and text2 should be orthogonal (similarity = 0.0)
      expect(matrix.matrix[0][1]).toBeCloseTo(0.0, 5);
      expect(matrix.matrix[1][0]).toBeCloseTo(0.0, 5);
    });

    it('should include correct metadata in similarity matrix', async () => {
      const embeddings: EmbeddingVector[] = [
        {
          textId: 'text1',
          model: 'test-model',
          vector: [1, 1],
          dimensions: 2,
          metadata: {}
        },
        {
          textId: 'text2',
          model: 'test-model',
          vector: [1, -1],
          dimensions: 2,
          metadata: {}
        }
      ];

      const matrix = await similarityService.generateSimilarityMatrix(
        embeddings,
        'cosine',
        0.5
      );

      expect(matrix.model).toBe('test-model');
      expect(matrix.threshold).toBe(0.5);
      expect(matrix.metadata.totalComparisons).toBe(1); // Only off-diagonal comparisons
      expect(matrix.metadata.averageSimilarity).toBeDefined();
      expect(matrix.metadata.maxSimilarity).toBeDefined();
      expect(matrix.metadata.minSimilarity).toBeDefined();
      expect(matrix.metadata.processingTime).toBeGreaterThan(0);
      expect(matrix.metadata.timestamp).toBeInstanceOf(Date);
    });

    it('should handle single embedding vector', async () => {
      const embeddings: EmbeddingVector[] = [
        {
          textId: 'text1',
          model: 'test-model',
          vector: [1, 2, 3],
          dimensions: 3,
          metadata: {}
        }
      ];

      const matrix = await similarityService.generateSimilarityMatrix(
        embeddings,
        'cosine',
        0.8
      );

      expect(matrix.textIds).toEqual(['text1']);
      expect(matrix.matrix).toEqual([[1.0]]);
      expect(matrix.metadata.totalComparisons).toBe(0);
    });
  });

  describe('error handling', () => {
    it('should throw error for mismatched vector dimensions', () => {
      const vector1: EmbeddingVector = {
        textId: 'text1',
        model: 'test-model',
        vector: [1, 2, 3],
        dimensions: 3,
        metadata: {}
      };

      const vector2: EmbeddingVector = {
        textId: 'text2',
        model: 'test-model',
        vector: [1, 2],
        dimensions: 2,
        metadata: {}
      };

      expect(() => {
        similarityService.calculateCosineSimilarity(vector1, vector2);
      }).toThrow('Vector dimensions must match');
    });

    it('should throw error for empty embeddings array', async () => {
      await expect(
        similarityService.generateSimilarityMatrix([], 'cosine', 0.8)
      ).rejects.toThrow('At least one embedding vector is required');
    });

    it('should throw error for invalid similarity method', async () => {
      const embeddings: EmbeddingVector[] = [
        {
          textId: 'text1',
          model: 'test-model',
          vector: [1, 2, 3],
          dimensions: 3,
          metadata: {}
        }
      ];

      await expect(
        similarityService.generateSimilarityMatrix(
          embeddings,
          'invalid-method' as any,
          0.8
        )
      ).rejects.toThrow('Unsupported similarity method');
    });

    it('should throw error for invalid threshold', async () => {
      const embeddings: EmbeddingVector[] = [
        {
          textId: 'text1',
          model: 'test-model',
          vector: [1, 2, 3],
          dimensions: 3,
          metadata: {}
        }
      ];

      await expect(
        similarityService.generateSimilarityMatrix(embeddings, 'cosine', 1.5)
      ).rejects.toThrow('Threshold must be between 0 and 1');

      await expect(
        similarityService.generateSimilarityMatrix(embeddings, 'cosine', -0.1)
      ).rejects.toThrow('Threshold must be between 0 and 1');
    });
  });

  describe('performance', () => {
    it('should handle large similarity matrices efficiently', async () => {
      const numTexts = 50;
      const embeddings: EmbeddingVector[] = Array.from({ length: numTexts }, (_, i) => ({
        textId: `text${i}`,
        model: 'test-model',
        vector: Array.from({ length: 100 }, () => Math.random()),
        dimensions: 100,
        metadata: {}
      }));

      const startTime = Date.now();
      const matrix = await similarityService.generateSimilarityMatrix(
        embeddings,
        'cosine',
        0.8
      );
      const endTime = Date.now();

      expect(matrix.textIds).toHaveLength(numTexts);
      expect(matrix.matrix).toHaveLength(numTexts);
      expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds
    });
  });
});
