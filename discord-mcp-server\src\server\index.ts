import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { 
  CallToolRequestSchema,
  ListToolsRequestSchema,
  Tool
} from '@modelcontextprotocol/sdk/types.js';
import { loggers } from '../utils/logger.js';
import { DiscordClient } from '../discord/client.js';
import { AuthManager } from '../auth/manager.js';
import { rateLimiter } from '../middleware/rate-limiter.js';
import { InputValidator } from '../middleware/validator.js';
import {
  SendMessageSchema,
  GetMessagesSchema,
  GetChannelInfoSchema,
  SearchMessagesSchema,
  ModerateContentSchema,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  RateLimitError
} from '../types/index.js';

// Import tool implementations
import { sendMessage } from '../tools/send-message.js';
import { getMessages } from '../tools/get-messages.js';
import { getChannelInfo } from '../tools/get-channel-info.js';
import { searchMessages } from '../tools/search-messages.js';
import { moderateContent } from '../tools/moderate-content.js';

/**
 * Setup the MCP server with all Discord tools and handlers
 */
export async function setupMCPServer(server: Server): Promise<void> {
  loggers.info('Setting up Discord MCP Server...');

  // Initialize Discord client manager
  const discordClient = new DiscordClient();
  
  // Initialize authentication manager
  const authManager = new AuthManager();

  // Define available tools
  const tools: Tool[] = [
    {
      name: 'send_message',
      description: 'Send a message to a Discord channel with optional embed',
      inputSchema: {
        type: 'object',
        properties: {
          channel_id: {
            type: 'string',
            description: 'The Discord channel ID to send the message to'
          },
          content: {
            type: 'string',
            description: 'The message content (max 2000 characters)'
          },
          embed: {
            type: 'object',
            description: 'Optional embed object',
            properties: {
              title: { type: 'string', description: 'Embed title' },
              description: { type: 'string', description: 'Embed description' },
              color: { type: 'number', description: 'Embed color (decimal)' },
              fields: {
                type: 'array',
                description: 'Embed fields',
                items: {
                  type: 'object',
                  properties: {
                    name: { type: 'string' },
                    value: { type: 'string' },
                    inline: { type: 'boolean' }
                  },
                  required: ['name', 'value']
                }
              }
            }
          }
        },
        required: ['channel_id', 'content']
      }
    },
    {
      name: 'get_messages',
      description: 'Retrieve message history from a Discord channel',
      inputSchema: {
        type: 'object',
        properties: {
          channel_id: {
            type: 'string',
            description: 'The Discord channel ID to get messages from'
          },
          limit: {
            type: 'number',
            description: 'Number of messages to retrieve (1-100, default: 50)',
            minimum: 1,
            maximum: 100
          },
          before: {
            type: 'string',
            description: 'Get messages before this message ID'
          },
          after: {
            type: 'string',
            description: 'Get messages after this message ID'
          }
        },
        required: ['channel_id']
      }
    },
    {
      name: 'get_channel_info',
      description: 'Get information about a Discord channel',
      inputSchema: {
        type: 'object',
        properties: {
          channel_id: {
            type: 'string',
            description: 'The Discord channel ID to get information about'
          }
        },
        required: ['channel_id']
      }
    },
    {
      name: 'search_messages',
      description: 'Search for messages in Discord channels with filters',
      inputSchema: {
        type: 'object',
        properties: {
          query: {
            type: 'string',
            description: 'Search query string'
          },
          channel_id: {
            type: 'string',
            description: 'Optional: Limit search to specific channel'
          },
          author_id: {
            type: 'string',
            description: 'Optional: Filter by message author'
          },
          date_from: {
            type: 'string',
            description: 'Optional: Start date for search (ISO string)'
          },
          date_to: {
            type: 'string',
            description: 'Optional: End date for search (ISO string)'
          },
          limit: {
            type: 'number',
            description: 'Number of results to return (1-50, default: 25)',
            minimum: 1,
            maximum: 50
          }
        },
        required: ['query']
      }
    },
    {
      name: 'moderate_content',
      description: 'Perform moderation actions on Discord content or users',
      inputSchema: {
        type: 'object',
        properties: {
          action: {
            type: 'string',
            enum: ['delete_message', 'timeout_user', 'ban_user', 'kick_user'],
            description: 'The moderation action to perform'
          },
          target_id: {
            type: 'string',
            description: 'The ID of the target (message ID or user ID)'
          },
          reason: {
            type: 'string',
            description: 'Optional reason for the moderation action'
          },
          duration: {
            type: 'number',
            description: 'Duration in seconds (for timeout_user action)',
            minimum: 60,
            maximum: 2419200 // 28 days max
          }
        },
        required: ['action', 'target_id']
      }
    }
  ];

  // Register list tools handler
  server.setRequestHandler(ListToolsRequestSchema, async () => {
    loggers.debug('Listing available tools');
    return { tools };
  });

  // Register call tool handler
  server.setRequestHandler(CallToolRequestSchema, async (request) => {
    const { name, arguments: args } = request.params;
    const startTime = Date.now();

    try {
      loggers.mcpRequest(name, args);

      // Validate authentication with enhanced auth manager
      const authResult = await authManager.validateRequest(request);
      if (!authResult.valid || !authResult.tenantId) {
        throw new AuthenticationError('Invalid authentication credentials');
      }

      const { tenantId, permissions } = authResult;

      // Check permissions for this tool
      if (!authManager.hasPermission(permissions || [], 'discord', name)) {
        throw new AuthorizationError(`Insufficient permissions for tool: ${name}`);
      }

      // Check rate limits
      await rateLimiter.checkLimit(tenantId, name);

      // Get Discord client for this tenant
      const client = await discordClient.getClient(tenantId);

      let result;
      let validatedArgs;

      // Validate and sanitize inputs based on tool type
      switch (name) {
        case 'send_message':
          validatedArgs = {
            channel_id: InputValidator.validateDiscordId(args.channel_id, 'channel_id'),
            content: InputValidator.validateMessageContent(args.content),
            embed: InputValidator.validateEmbed(args.embed)
          };
          result = await sendMessage(client, validatedArgs);
          break;

        case 'get_messages':
          const pagination = InputValidator.validatePagination(args.limit, args.before, args.after);
          validatedArgs = {
            channel_id: InputValidator.validateDiscordId(args.channel_id, 'channel_id'),
            ...pagination
          };
          result = await getMessages(client, validatedArgs);
          break;

        case 'get_channel_info':
          validatedArgs = {
            channel_id: InputValidator.validateDiscordId(args.channel_id, 'channel_id')
          };
          result = await getChannelInfo(client, validatedArgs);
          break;

        case 'search_messages':
          validatedArgs = {
            query: InputValidator.validateSearchQuery(args.query),
            channel_id: args.channel_id ? InputValidator.validateDiscordId(args.channel_id, 'channel_id') : undefined,
            author_id: args.author_id ? InputValidator.validateDiscordId(args.author_id, 'author_id') : undefined,
            date_from: InputValidator.validateDate(args.date_from, 'date_from')?.toISOString(),
            date_to: InputValidator.validateDate(args.date_to, 'date_to')?.toISOString(),
            limit: InputValidator.validateNumber(args.limit || 25, 'limit', { min: 1, max: 50, integer: true })
          };
          result = await searchMessages(client, validatedArgs);
          break;

        case 'moderate_content':
          validatedArgs = {
            action: InputValidator.validateModerationAction(args.action),
            target_id: InputValidator.validateDiscordId(args.target_id, 'target_id'),
            reason: args.reason ? InputValidator.validateString(args.reason, 'reason', { maxLength: 512 }) : undefined,
            duration: args.action === 'timeout_user' ? InputValidator.validateTimeoutDuration(args.duration) : undefined
          };
          result = await moderateContent(client, validatedArgs);
          break;

        default:
          throw new ValidationError(`Unknown tool: ${name}`);
      }

      // Log successful operation for audit
      await authManager.logAuditEvent(
        tenantId,
        `TOOL_EXECUTED_${name.toUpperCase()}`,
        'discord_tool',
        name,
        {
          arguments: validatedArgs,
          result: { success: true }
        }
      );

      const duration = Date.now() - startTime;
      loggers.mcpResponse(name, true, duration);
      
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2)
          }
        ]
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      loggers.mcpResponse(name, false, duration, errorMessage);
      loggers.error(`Tool execution failed: ${name}`, error as Error);

      // Log failed operation for audit (if we have tenant info)
      try {
        const authResult = await authManager.validateRequest(request);
        if (authResult.valid && authResult.tenantId) {
          await authManager.logAuditEvent(
            authResult.tenantId,
            `TOOL_FAILED_${name.toUpperCase()}`,
            'discord_tool',
            name,
            {
              arguments: args,
              error: errorMessage,
              errorType: error instanceof Error ? error.constructor.name : 'Unknown'
            }
          );
        }
      } catch (auditError) {
        loggers.error('Failed to log audit event for error', auditError as Error);
      }

      // Return appropriate error response based on error type
      let statusCode = 500;
      if (error instanceof ValidationError) {
        statusCode = 400;
      } else if (error instanceof AuthenticationError) {
        statusCode = 401;
      } else if (error instanceof AuthorizationError) {
        statusCode = 403;
      } else if (error instanceof RateLimitError) {
        statusCode = 429;
      }

      return {
        content: [
          {
            type: 'text',
            text: `Error (${statusCode}): ${errorMessage}`
          }
        ],
        isError: true
      };
    }
  });

  loggers.info('Discord MCP Server setup completed', {
    toolsRegistered: tools.length,
    tools: tools.map(t => t.name)
  });
}
