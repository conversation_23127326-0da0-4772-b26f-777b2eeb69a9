import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Play, Settings, Download, Al<PERSON><PERSON>riangle, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'lucide-react';
import TextInputManager from '../components/TextInputManager';
import SimilarityMatrix from '../components/SimilarityMatrix';
import { useAnalysis } from '../hooks/useAnalysis';
import { API_CONFIG } from '../config';

interface TextInput {
  id: string;
  content: string;
  label: string;
}

interface AnalysisOptions {
  models: string[];
  threshold: number;
  enableCloneDetection: boolean;
  enableModelComparison: boolean;
}

const AnalysisPage: React.FC = () => {
  const [texts, setTexts] = useState<TextInput[]>([]);
  const [options, setOptions] = useState<AnalysisOptions>({
    models: ['text-embedding-ada-002'],
    threshold: 0.8,
    enableCloneDetection: true,
    enableModelComparison: true,
  });
  const [showSettings, setShowSettings] = useState(false);
  const [selectedModel, setSelectedModel] = useState<string>('');

  const { analyzeTexts, isLoading, data, error, availableModels } = useAnalysis();

  // Handle text changes
  const handleTextsChange = useCallback((newTexts: TextInput[]) => {
    setTexts(newTexts);
  }, []);

  // Handle analysis start
  const handleAnalyze = useCallback(async () => {
    if (texts.length < 2) return;

    const validTexts = texts.filter(text => text.content.trim().length >= 10);
    if (validTexts.length < 2) return;

    try {
      await analyzeTexts({
        texts: validTexts.map(text => ({
          id: text.id,
          content: text.content,
          label: text.label,
        })),
        options: {
          ...options,
          preprocessingOptions: {
            removeStopWords: false,
            stemming: false,
            lemmatization: true,
            removeNumbers: false,
            removePunctuation: false,
            toLowerCase: true,
            removeExtraWhitespace: true,
            minWordLength: 1,
          },
        },
      });
    } catch (err) {
      console.error('Analysis failed:', err);
    }
  }, [texts, options, analyzeTexts]);

  // Check if analysis can be started
  const canAnalyze = texts.length >= 2 && 
    texts.filter(text => text.content.trim().length >= 10).length >= 2 && 
    !isLoading;

  // Get text labels for matrix display
  const textLabels = texts.reduce((acc, text) => {
    acc[text.id] = text.label;
    return acc;
  }, {} as Record<string, string>);

  // Handle matrix cell click
  const handleMatrixCellClick = (textId1: string, textId2: string, similarity: number) => {
    console.log(`Clicked: ${textId1} vs ${textId2} - ${(similarity * 100).toFixed(1)}%`);
    // TODO: Show detailed comparison modal
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            Plagiarism Detector
          </h1>
          <p className="text-lg text-gray-600">
            Advanced semantic similarity analysis powered by AI
          </p>
        </motion.div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          {/* Left Column - Input and Settings */}
          <div className="xl:col-span-2 space-y-6">
            {/* Text Input Manager */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
            >
              <TextInputManager
                onTextsChange={handleTextsChange}
                maxTexts={10}
                minTexts={2}
              />
            </motion.div>

            {/* Analysis Controls */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white rounded-lg border border-gray-200 shadow-sm p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Analysis Settings</h3>
                <button
                  onClick={() => setShowSettings(!showSettings)}
                  className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded transition-colors"
                >
                  <Settings className="h-5 w-5" />
                </button>
              </div>

              <AnimatePresence>
                {showSettings && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="space-y-4 mb-4"
                  >
                    {/* Model Selection */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Embedding Models
                      </label>
                      <div className="space-y-2">
                        {availableModels?.map((model) => (
                          <label key={model.name} className="flex items-center">
                            <input
                              type="checkbox"
                              checked={options.models.includes(model.name)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setOptions(prev => ({
                                    ...prev,
                                    models: [...prev.models, model.name]
                                  }));
                                } else {
                                  setOptions(prev => ({
                                    ...prev,
                                    models: prev.models.filter(m => m !== model.name)
                                  }));
                                }
                              }}
                              className="mr-2"
                            />
                            <span className="text-sm text-gray-700">{model.displayName}</span>
                          </label>
                        ))}
                      </div>
                    </div>

                    {/* Threshold */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Similarity Threshold: {(options.threshold * 100).toFixed(0)}%
                      </label>
                      <input
                        type="range"
                        min="0.5"
                        max="1"
                        step="0.05"
                        value={options.threshold}
                        onChange={(e) => setOptions(prev => ({
                          ...prev,
                          threshold: parseFloat(e.target.value)
                        }))}
                        className="w-full"
                      />
                    </div>

                    {/* Options */}
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={options.enableCloneDetection}
                          onChange={(e) => setOptions(prev => ({
                            ...prev,
                            enableCloneDetection: e.target.checked
                          }))}
                          className="mr-2"
                        />
                        <span className="text-sm text-gray-700">Enable Clone Detection</span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={options.enableModelComparison}
                          onChange={(e) => setOptions(prev => ({
                            ...prev,
                            enableModelComparison: e.target.checked
                          }))}
                          className="mr-2"
                        />
                        <span className="text-sm text-gray-700">Enable Model Comparison</span>
                      </label>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Analyze Button */}
              <motion.button
                onClick={handleAnalyze}
                disabled={!canAnalyze}
                className={`w-full py-3 px-6 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center space-x-2 ${
                  canAnalyze
                    ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
                whileHover={canAnalyze ? { scale: 1.02 } : {}}
                whileTap={canAnalyze ? { scale: 0.98 } : {}}
              >
                {isLoading ? (
                  <>
                    <Clock className="h-5 w-5 animate-spin" />
                    <span>Analyzing...</span>
                  </>
                ) : (
                  <>
                    <Brain className="h-5 w-5" />
                    <span>Analyze Texts</span>
                  </>
                )}
              </motion.button>
            </motion.div>
          </div>

          {/* Right Column - Status and Quick Stats */}
          <div className="space-y-6">
            {/* Status Card */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-white rounded-lg border border-gray-200 shadow-sm p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Status</h3>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Texts Ready</span>
                  <div className="flex items-center space-x-1">
                    {texts.filter(t => t.content.trim().length >= 10).length >= 2 ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <AlertTriangle className="h-4 w-4 text-yellow-600" />
                    )}
                    <span className="text-sm font-medium">
                      {texts.filter(t => t.content.trim().length >= 10).length}/{texts.length}
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Models Selected</span>
                  <span className="text-sm font-medium">{options.models.length}</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Threshold</span>
                  <span className="text-sm font-medium">{(options.threshold * 100).toFixed(0)}%</span>
                </div>
              </div>
            </motion.div>

            {/* Results Summary */}
            {data && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="bg-white rounded-lg border border-gray-200 shadow-sm p-6"
              >
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Results Summary</h3>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Processing Time</span>
                    <span className="text-sm font-medium">{data.summary.processingTime}ms</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Clones Found</span>
                    <span className={`text-sm font-medium ${
                      data.summary.clonesFound > 0 ? 'text-red-600' : 'text-green-600'
                    }`}>
                      {data.summary.clonesFound}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Avg Similarity</span>
                    <span className="text-sm font-medium">
                      {(data.summary.averageSimilarity * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        </div>

        {/* Results Section */}
        {data && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="mt-8 space-y-6"
          >
            {/* Model Tabs */}
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="border-b border-gray-200">
                <nav className="flex space-x-8 px-6">
                  {Object.keys(data.similarityMatrices).map((modelName) => (
                    <button
                      key={modelName}
                      onClick={() => setSelectedModel(modelName)}
                      className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                        selectedModel === modelName || (selectedModel === '' && modelName === Object.keys(data.similarityMatrices)[0])
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      {availableModels?.find(m => m.name === modelName)?.displayName || modelName}
                    </button>
                  ))}
                </nav>
              </div>

              <div className="p-6">
                {Object.entries(data.similarityMatrices).map(([modelName, matrix]) => {
                  const isActive = selectedModel === modelName || (selectedModel === '' && modelName === Object.keys(data.similarityMatrices)[0]);
                  
                  return isActive ? (
                    <SimilarityMatrix
                      key={modelName}
                      matrix={matrix.matrix}
                      textIds={matrix.textIds}
                      textLabels={textLabels}
                      model={modelName}
                      threshold={options.threshold}
                      onCellClick={handleMatrixCellClick}
                    />
                  ) : null;
                })}
              </div>
            </div>
          </motion.div>
        )}

        {/* Error Display */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-8 bg-red-50 border border-red-200 rounded-lg p-4"
          >
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <span className="text-red-800 font-medium">Analysis Failed</span>
            </div>
            <p className="text-red-700 mt-2">{error}</p>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default AnalysisPage;
