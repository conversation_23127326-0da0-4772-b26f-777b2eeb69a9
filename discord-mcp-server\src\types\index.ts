import { z } from 'zod';

// Discord Types
export interface DiscordMessage {
  id: string;
  content: string;
  author: {
    id: string;
    username: string;
    discriminator: string;
  };
  channel_id: string;
  guild_id?: string;
  timestamp: string;
  edited_timestamp?: string;
  embeds?: DiscordEmbed[];
}

export interface DiscordEmbed {
  title?: string;
  description?: string;
  color?: number;
  fields?: Array<{
    name: string;
    value: string;
    inline?: boolean;
  }>;
  footer?: {
    text: string;
    icon_url?: string;
  };
  timestamp?: string;
}

export interface DiscordChannel {
  id: string;
  name: string;
  type: number;
  guild_id?: string;
  position?: number;
  permission_overwrites?: any[];
  topic?: string;
  nsfw?: boolean;
  last_message_id?: string;
  parent_id?: string;
}

// Authentication Types
export interface TenantConfig {
  id: string;
  name: string;
  discordBotToken: string;
  permissions: Permission[];
  quotas: ResourceQuotas;
  createdAt: Date;
  updatedAt: Date;
}

export interface Permission {
  resource: string;
  actions: string[];
  channels?: string[];
  roles?: string[];
}

export interface ResourceQuotas {
  maxRequestsPerMinute: number;
  maxRequestsPerHour: number;
  maxMessageHistory: number;
  maxSearchResults: number;
}

export interface ApiKey {
  id: string;
  key: string;
  tenantId: string;
  name: string;
  permissions: Permission[];
  expiresAt?: Date;
  lastUsedAt?: Date;
  createdAt: Date;
}

// MCP Tool Schemas
export const SendMessageSchema = z.object({
  channel_id: z.string().min(1, "Channel ID is required"),
  content: z.string().min(1, "Message content is required").max(2000, "Message too long"),
  embed: z.object({
    title: z.string().optional(),
    description: z.string().optional(),
    color: z.number().optional(),
    fields: z.array(z.object({
      name: z.string(),
      value: z.string(),
      inline: z.boolean().optional()
    })).optional()
  }).optional()
});

export const GetMessagesSchema = z.object({
  channel_id: z.string().min(1, "Channel ID is required"),
  limit: z.number().min(1).max(100).default(50),
  before: z.string().optional(),
  after: z.string().optional()
});

export const GetChannelInfoSchema = z.object({
  channel_id: z.string().min(1, "Channel ID is required")
});

export const SearchMessagesSchema = z.object({
  query: z.string().min(1, "Search query is required"),
  channel_id: z.string().optional(),
  author_id: z.string().optional(),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  limit: z.number().min(1).max(50).default(25)
});

export const ModerateContentSchema = z.object({
  action: z.enum(['delete_message', 'timeout_user', 'ban_user', 'kick_user']),
  target_id: z.string().min(1, "Target ID is required"),
  reason: z.string().optional(),
  duration: z.number().optional() // For timeouts, in seconds
});

// Audit Log Types
export interface AuditLogEntry {
  id: string;
  tenantId: string;
  userId?: string;
  action: string;
  resource: string;
  resourceId: string;
  details: Record<string, any>;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
}

// Rate Limiting Types
export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests: boolean;
  keyGenerator?: (req: any) => string;
}

// Configuration Types
export interface ServerConfig {
  name: string;
  version: string;
  port: number;
  cors: {
    origin: string;
    credentials: boolean;
  };
  rateLimit: RateLimitConfig;
  database: {
    url: string;
    maxConnections: number;
  };
  logging: {
    level: string;
    file: string;
    maxSize: string;
    maxFiles: number;
  };
  security: {
    jwtSecret: string;
    encryptionKey: string;
  };
  discord: {
    apiVersion: string;
    intents: string[];
  };
}

// Error Types
export class MCPError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500,
    public details?: any
  ) {
    super(message);
    this.name = 'MCPError';
  }
}

export class AuthenticationError extends MCPError {
  constructor(message: string = 'Authentication failed') {
    super(message, 'AUTHENTICATION_ERROR', 401);
  }
}

export class AuthorizationError extends MCPError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 'AUTHORIZATION_ERROR', 403);
  }
}

export class ValidationError extends MCPError {
  constructor(message: string, details?: any) {
    super(message, 'VALIDATION_ERROR', 400, details);
  }
}

export class RateLimitError extends MCPError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 'RATE_LIMIT_ERROR', 429);
  }
}

export class DiscordAPIError extends MCPError {
  constructor(message: string, details?: any) {
    super(message, 'DISCORD_API_ERROR', 502, details);
  }
}
