import '@testing-library/jest-dom';
import { vi } from 'vitest';

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: vi.fn(),
});

// Mock fetch
global.fetch = vi.fn();

// Mock environment variables
vi.mock('../config', () => ({
  API_CONFIG: {
    BASE_URL: 'http://localhost:5000',
    TIMEOUT: 30000,
  },
}));

// Clean up after each test
afterEach(() => {
  vi.clearAllMocks();
});

// Global test utilities
global.testUtils = {
  createMockAnalysisResponse: () => ({
    requestId: 'test-request-id',
    processedTexts: [
      {
        textId: 'text1',
        originalText: 'Original text 1',
        processedText: 'processed text 1',
        wordCount: 3,
        characterCount: 17,
        language: 'en',
        processingTime: 100,
        metadata: {},
      },
      {
        textId: 'text2',
        originalText: 'Original text 2',
        processedText: 'processed text 2',
        wordCount: 3,
        characterCount: 17,
        language: 'en',
        processingTime: 100,
        metadata: {},
      },
    ],
    embeddings: {
      'test-model': [
        {
          textId: 'text1',
          model: 'test-model',
          vector: [0.1, 0.2, 0.3, 0.4],
          dimensions: 4,
          metadata: {},
        },
        {
          textId: 'text2',
          model: 'test-model',
          vector: [0.5, 0.6, 0.7, 0.8],
          dimensions: 4,
          metadata: {},
        },
      ],
    },
    similarityMatrices: {
      'test-model': {
        textIds: ['text1', 'text2'],
        matrix: [
          [1.0, 0.85],
          [0.85, 1.0],
        ],
        model: 'test-model',
        threshold: 0.8,
        metadata: {
          totalComparisons: 1,
          averageSimilarity: 0.85,
          maxSimilarity: 0.85,
          minSimilarity: 0.85,
          clonesDetected: 1,
          processingTime: 1000,
          timestamp: new Date(),
        },
      },
    },
    cloneDetectionResults: [
      {
        textId1: 'text1',
        textId2: 'text2',
        similarity: 0.85,
        model: 'test-model',
        confidence: 'high',
        evidence: {
          commonPhrases: ['shared phrase'],
          structuralSimilarity: 0.8,
          semanticSimilarity: 0.85,
        },
      },
    ],
    summary: {
      totalTexts: 2,
      modelsUsed: ['test-model'],
      processingTime: 2000,
      clonesFound: 1,
      averageSimilarity: 0.85,
      recommendations: ['Review detected similarity'],
    },
    timestamp: new Date(),
  }),

  createMockTextInput: (id: string, content: string, label?: string) => ({
    id,
    content,
    label: label || `Document ${id}`,
  }),

  delay: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
};
