{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "outDir": "./dist", "rootDir": "./src", "strict": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "noUncheckedIndexedAccess": false, "exactOptionalPropertyTypes": false, "noImplicitThis": false, "noPropertyAccessFromIndexSignature": false, "suppressImplicitAnyIndexErrors": true, "declaration": false, "declarationMap": false, "sourceMap": false, "removeComments": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "lib": ["ES2022"], "types": ["node"]}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "dist", "src/**/*.test.ts", "src/**/*.spec.ts", "src/__tests__/**/*"]}