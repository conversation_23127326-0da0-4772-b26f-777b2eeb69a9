@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  /* Custom button styles */
  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold py-2 px-4 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 hover:from-blue-700 hover:to-purple-700;
  }
  
  .btn-secondary {
    @apply bg-white text-gray-700 font-medium py-2 px-4 rounded-lg border border-gray-300 shadow-sm hover:shadow-md transition-all duration-200 hover:bg-gray-50;
  }
  
  /* Custom card styles */
  .card {
    @apply bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200;
  }
  
  /* Custom input styles */
  .input-primary {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200;
  }
  
  /* Loading animation */
  .loading-spinner {
    @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600;
  }
  
  /* Gradient backgrounds */
  .gradient-bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .gradient-bg-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }
  
  .gradient-bg-success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }
  
  /* Similarity color coding */
  .similarity-very-high {
    @apply bg-red-100 border-red-300 text-red-800;
  }
  
  .similarity-high {
    @apply bg-orange-100 border-orange-300 text-orange-800;
  }
  
  .similarity-medium {
    @apply bg-yellow-100 border-yellow-300 text-yellow-800;
  }
  
  .similarity-low {
    @apply bg-green-100 border-green-300 text-green-800;
  }
  
  /* Matrix cell hover effects */
  .matrix-cell {
    @apply transition-all duration-200 hover:scale-105 hover:z-10 cursor-pointer;
  }
  
  /* Smooth animations */
  .fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .scale-in {
    animation: scaleIn 0.2s ease-out;
  }
}

@layer utilities {
  /* Custom scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e0 #f7fafc;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-gray-100 rounded-full;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full hover:bg-gray-400;
  }
  
  /* Text truncation */
  .truncate-2-lines {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .truncate-3-lines {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Keyframe animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Loading states */
.loading-skeleton {
  @apply animate-pulse bg-gray-200 rounded;
}

.loading-text {
  @apply h-4 bg-gray-200 rounded animate-pulse;
}

/* Focus styles for accessibility */
.focus-visible:focus-visible {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break-before {
    page-break-before: always;
  }
  
  .print-break-after {
    page-break-after: always;
  }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  .dark-mode {
    @apply bg-gray-900 text-gray-100;
  }
  
  .dark-mode .card {
    @apply bg-gray-800 border-gray-700;
  }
  
  .dark-mode .input-primary {
    @apply bg-gray-800 border-gray-600 text-gray-100;
  }
}
