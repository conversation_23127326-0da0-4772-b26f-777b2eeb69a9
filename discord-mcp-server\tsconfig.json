{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "resolveJsonModule": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "lib": ["ES2022", "DOM"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests", "**/*.test.ts", "**/*.spec.ts"]}