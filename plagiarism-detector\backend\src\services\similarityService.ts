import { Matrix } from 'ml-matrix';
import { Embedding<PERSON><PERSON>, SimilarityPair, SimilarityMatrix, SimilarityMethod } from '../types/index.js';

/**
 * Similarity Calculation Service
 * Handles pairwise similarity calculations and matrix generation
 */
export class SimilarityService {
  /**
   * Calculate cosine similarity between two vectors
   */
  private cosineSimilarity(vectorA: number[], vectorB: number[]): number {
    if (vectorA.length !== vectorB.length) {
      throw new Error('Vectors must have the same dimensions');
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < vectorA.length; i++) {
      dotProduct += vectorA[i] * vectorB[i];
      normA += vectorA[i] * vectorA[i];
      normB += vectorB[i] * vectorB[i];
    }

    normA = Math.sqrt(normA);
    normB = Math.sqrt(normB);

    if (normA === 0 || normB === 0) {
      return 0; // Handle zero vectors
    }

    return dotProduct / (normA * normB);
  }

  /**
   * Calculate Euclidean distance between two vectors
   */
  private euclideanDistance(vectorA: number[], vectorB: number[]): number {
    if (vectorA.length !== vectorB.length) {
      throw new Error('Vectors must have the same dimensions');
    }

    let sum = 0;
    for (let i = 0; i < vectorA.length; i++) {
      const diff = vectorA[i] - vectorB[i];
      sum += diff * diff;
    }

    return Math.sqrt(sum);
  }

  /**
   * Calculate Manhattan distance between two vectors
   */
  private manhattanDistance(vectorA: number[], vectorB: number[]): number {
    if (vectorA.length !== vectorB.length) {
      throw new Error('Vectors must have the same dimensions');
    }

    let sum = 0;
    for (let i = 0; i < vectorA.length; i++) {
      sum += Math.abs(vectorA[i] - vectorB[i]);
    }

    return sum;
  }

  /**
   * Calculate similarity between two vectors using specified method
   */
  calculateSimilarity(
    vectorA: number[],
    vectorB: number[],
    method: SimilarityMethod = 'cosine'
  ): number {
    switch (method) {
      case 'cosine':
        return this.cosineSimilarity(vectorA, vectorB);
      case 'euclidean':
        // Convert distance to similarity (0-1 range)
        const euclideanDist = this.euclideanDistance(vectorA, vectorB);
        return 1 / (1 + euclideanDist);
      case 'manhattan':
        // Convert distance to similarity (0-1 range)
        const manhattanDist = this.manhattanDistance(vectorA, vectorB);
        return 1 / (1 + manhattanDist);
      default:
        throw new Error(`Unsupported similarity method: ${method}`);
    }
  }

  /**
   * Calculate pairwise similarities for all embedding pairs
   */
  calculatePairwiseSimilarities(
    embeddings: EmbeddingVector[],
    method: SimilarityMethod = 'cosine'
  ): SimilarityPair[] {
    const startTime = Date.now();
    const pairs: SimilarityPair[] = [];

    for (let i = 0; i < embeddings.length; i++) {
      for (let j = i + 1; j < embeddings.length; j++) {
        const embeddingA = embeddings[i];
        const embeddingB = embeddings[j];

        // Ensure embeddings are from the same model
        if (embeddingA.model !== embeddingB.model) {
          continue;
        }

        const similarity = this.calculateSimilarity(
          embeddingA.vector,
          embeddingB.vector,
          method
        );

        pairs.push({
          textId1: embeddingA.textId,
          textId2: embeddingB.textId,
          similarity,
          model: embeddingA.model,
          metadata: {
            calculationMethod: method,
            processingTime: Date.now() - startTime,
            timestamp: new Date(),
          },
        });
      }
    }

    return pairs;
  }

  /**
   * Generate similarity matrix for embeddings
   */
  generateSimilarityMatrix(
    embeddings: EmbeddingVector[],
    threshold: number = 0.8,
    method: SimilarityMethod = 'cosine'
  ): SimilarityMatrix {
    const startTime = Date.now();
    
    if (embeddings.length === 0) {
      throw new Error('No embeddings provided for similarity matrix generation');
    }

    // Ensure all embeddings are from the same model
    const model = embeddings[0].model;
    const filteredEmbeddings = embeddings.filter(emb => emb.model === model);
    
    if (filteredEmbeddings.length !== embeddings.length) {
      console.warn(`Filtered embeddings from ${embeddings.length} to ${filteredEmbeddings.length} for model consistency`);
    }

    const textIds = filteredEmbeddings.map(emb => emb.textId);
    const n = filteredEmbeddings.length;
    const matrix: number[][] = Array(n).fill(null).map(() => Array(n).fill(0));

    let totalSimilarity = 0;
    let maxSimilarity = -1;
    let minSimilarity = 1;
    let clonesDetected = 0;
    let totalComparisons = 0;

    // Calculate similarity matrix
    for (let i = 0; i < n; i++) {
      for (let j = 0; j < n; j++) {
        if (i === j) {
          matrix[i][j] = 1; // Self-similarity is always 1
        } else {
          const similarity = this.calculateSimilarity(
            filteredEmbeddings[i].vector,
            filteredEmbeddings[j].vector,
            method
          );
          
          matrix[i][j] = similarity;
          
          // Update statistics (only count upper triangle to avoid double counting)
          if (i < j) {
            totalSimilarity += similarity;
            maxSimilarity = Math.max(maxSimilarity, similarity);
            minSimilarity = Math.min(minSimilarity, similarity);
            totalComparisons++;
            
            if (similarity >= threshold) {
              clonesDetected++;
            }
          }
        }
      }
    }

    const averageSimilarity = totalComparisons > 0 ? totalSimilarity / totalComparisons : 0;
    const processingTime = Date.now() - startTime;

    return {
      textIds,
      matrix,
      model,
      threshold,
      metadata: {
        totalComparisons,
        averageSimilarity,
        maxSimilarity: maxSimilarity === -1 ? 1 : maxSimilarity,
        minSimilarity: minSimilarity === 1 ? 0 : minSimilarity,
        clonesDetected,
        processingTime,
        timestamp: new Date(),
      },
    };
  }

  /**
   * Generate similarity matrices for multiple models
   */
  generateMultiModelSimilarityMatrices(
    embeddingsByModel: Record<string, EmbeddingVector[]>,
    threshold: number = 0.8,
    method: SimilarityMethod = 'cosine'
  ): Record<string, SimilarityMatrix> {
    const matrices: Record<string, SimilarityMatrix> = {};

    for (const [modelName, embeddings] of Object.entries(embeddingsByModel)) {
      try {
        if (embeddings.length > 0) {
          matrices[modelName] = this.generateSimilarityMatrix(embeddings, threshold, method);
        }
      } catch (error) {
        console.error(`Failed to generate similarity matrix for model ${modelName}:`, error);
      }
    }

    return matrices;
  }

  /**
   * Find most similar text pairs above threshold
   */
  findSimilarPairs(
    similarityMatrix: SimilarityMatrix,
    threshold: number = 0.8
  ): Array<{ textId1: string; textId2: string; similarity: number }> {
    const pairs: Array<{ textId1: string; textId2: string; similarity: number }> = [];
    const { textIds, matrix } = similarityMatrix;

    for (let i = 0; i < matrix.length; i++) {
      for (let j = i + 1; j < matrix[i].length; j++) {
        const similarity = matrix[i][j];
        if (similarity >= threshold) {
          pairs.push({
            textId1: textIds[i],
            textId2: textIds[j],
            similarity,
          });
        }
      }
    }

    // Sort by similarity (highest first)
    return pairs.sort((a, b) => b.similarity - a.similarity);
  }

  /**
   * Calculate similarity statistics for a matrix
   */
  calculateStatistics(similarityMatrix: SimilarityMatrix): {
    mean: number;
    median: number;
    standardDeviation: number;
    percentiles: { p25: number; p75: number; p90: number; p95: number };
    distribution: { low: number; medium: number; high: number; veryHigh: number };
  } {
    const { matrix } = similarityMatrix;
    const values: number[] = [];

    // Extract upper triangle values (excluding diagonal)
    for (let i = 0; i < matrix.length; i++) {
      for (let j = i + 1; j < matrix[i].length; j++) {
        values.push(matrix[i][j]);
      }
    }

    if (values.length === 0) {
      return {
        mean: 0,
        median: 0,
        standardDeviation: 0,
        percentiles: { p25: 0, p75: 0, p90: 0, p95: 0 },
        distribution: { low: 0, medium: 0, high: 0, veryHigh: 0 },
      };
    }

    values.sort((a, b) => a - b);

    // Calculate statistics
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const median = values[Math.floor(values.length / 2)];
    
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    const standardDeviation = Math.sqrt(variance);

    // Calculate percentiles
    const getPercentile = (p: number) => values[Math.floor(values.length * p / 100)];
    const percentiles = {
      p25: getPercentile(25),
      p75: getPercentile(75),
      p90: getPercentile(90),
      p95: getPercentile(95),
    };

    // Calculate distribution
    const distribution = {
      low: values.filter(v => v < 0.3).length,
      medium: values.filter(v => v >= 0.3 && v < 0.6).length,
      high: values.filter(v => v >= 0.6 && v < 0.8).length,
      veryHigh: values.filter(v => v >= 0.8).length,
    };

    return {
      mean,
      median,
      standardDeviation,
      percentiles,
      distribution,
    };
  }

  /**
   * Normalize similarity matrix values to 0-1 range
   */
  normalizeSimilarityMatrix(matrix: number[][]): number[][] {
    const flatValues = matrix.flat().filter(val => val !== 1); // Exclude diagonal
    const min = Math.min(...flatValues);
    const max = Math.max(...flatValues);
    const range = max - min;

    if (range === 0) return matrix; // All values are the same

    return matrix.map(row =>
      row.map(val => val === 1 ? 1 : (val - min) / range)
    );
  }
}
