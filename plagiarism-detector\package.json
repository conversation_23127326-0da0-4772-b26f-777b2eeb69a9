{"name": "plagiarism-detector", "version": "1.0.0", "description": "Semantic Similarity Analyzer for Plagiarism Detection using Multiple Embedding Models", "main": "backend/dist/index.js", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "cd backend && npm start", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "clean": "rimraf backend/dist frontend/dist backend/node_modules frontend/node_modules node_modules"}, "keywords": ["plagiarism-detection", "semantic-similarity", "embeddings", "nlp", "machine-learning", "text-analysis", "similarity-matrix", "clone-detection"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "rimraf": "^5.0.5"}, "workspaces": ["backend", "frontend"], "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/shaonidutta/w4d3-understanding-RAG"}}