# API Configuration
VITE_API_BASE_URL=http://localhost:3001
VITE_API_TIMEOUT=30000

# Application Configuration
VITE_APP_NAME=Plagiarism Detector
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=Semantic Similarity Analyzer

# Feature Flags
VITE_ENABLE_3D_VISUALIZATION=true
VITE_ENABLE_MODEL_COMPARISON=true
VITE_ENABLE_ADVANCED_ANALYTICS=true
VITE_ENABLE_EXPORT_FEATURES=true

# UI Configuration
VITE_DEFAULT_THEME=light
VITE_ENABLE_DARK_MODE=true
VITE_ANIMATION_DURATION=300
VITE_ENABLE_SOUND_EFFECTS=false

# Similarity Detection UI
VITE_DEFAULT_SIMILARITY_THRESHOLD=80
VITE_MAX_TEXT_INPUTS=10
VITE_MAX_TEXT_LENGTH=10000
VITE_SHOW_SIMILARITY_PERCENTAGES=true

# Development
VITE_DEBUG_MODE=false
VITE_ENABLE_DEVTOOLS=true
VITE_LOG_LEVEL=info

# Analytics (Optional)
VITE_ENABLE_ANALYTICS=false
VITE_ANALYTICS_ID=

# Performance
VITE_ENABLE_SERVICE_WORKER=false
VITE_CACHE_DURATION=300000
