# Server Configuration
PORT=3001
NODE_ENV=development
API_BASE_URL=http://localhost:3001

# CORS Configuration
FRONTEND_URL=http://localhost:5173
ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=text-embedding-ada-002
OPENAI_MAX_TOKENS=8191

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false

# Similarity Detection
DEFAULT_SIMILARITY_THRESHOLD=0.8
MAX_TEXT_LENGTH=10000
MAX_TEXTS_PER_REQUEST=10

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE_PATH=logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# Security
API_SECRET_KEY=your_secret_key_here
ENABLE_REQUEST_LOGGING=true
ENABLE_SECURITY_HEADERS=true

# Performance
EMBEDDING_CACHE_TTL=3600
MAX_CONCURRENT_REQUESTS=50
REQUEST_TIMEOUT=30000

# Model Configuration
SENTENCE_TRANSFORMER_MODEL=all-MiniLM-L6-v2
ALTERNATIVE_MODEL=all-mpnet-base-v2
ENABLE_MODEL_COMPARISON=true

# Development
DEBUG_MODE=false
ENABLE_CORS=true
TRUST_PROXY=false
