import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';

// Simple component for testing
const TestComponent = ({ title, count }: { title: string; count: number }) => {
  return (
    <div>
      <h1>{title}</h1>
      <p>Count: {count}</p>
      <button>Click me</button>
    </div>
  );
};

describe('Simple Frontend Tests', () => {
  it('should render a basic component', () => {
    render(<TestComponent title="Test Title" count={5} />);
    
    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByText('Count: 5')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Click me' })).toBeInTheDocument();
  });

  it('should handle different props', () => {
    render(<TestComponent title="Another Title" count={10} />);
    
    expect(screen.getByText('Another Title')).toBeInTheDocument();
    expect(screen.getByText('Count: 10')).toBeInTheDocument();
  });

  it('should render headings correctly', () => {
    render(<TestComponent title="Heading Test" count={0} />);
    
    const heading = screen.getByRole('heading', { level: 1 });
    expect(heading).toHaveTextContent('Heading Test');
  });

  it('should render buttons correctly', () => {
    render(<TestComponent title="Button Test" count={1} />);
    
    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('Click me');
  });

  it('should handle zero count', () => {
    render(<TestComponent title="Zero Count" count={0} />);
    
    expect(screen.getByText('Count: 0')).toBeInTheDocument();
  });

  it('should handle negative count', () => {
    render(<TestComponent title="Negative Count" count={-5} />);
    
    expect(screen.getByText('Count: -5')).toBeInTheDocument();
  });

  it('should handle empty title', () => {
    render(<TestComponent title="" count={1} />);
    
    const heading = screen.getByRole('heading', { level: 1 });
    expect(heading).toHaveTextContent('');
  });

  it('should handle large numbers', () => {
    render(<TestComponent title="Large Number" count={999999} />);
    
    expect(screen.getByText('Count: 999999')).toBeInTheDocument();
  });
});
