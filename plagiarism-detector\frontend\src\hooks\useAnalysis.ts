import { useState, useEffect, useCallback } from 'react';
import { API_CONFIG } from '../config';

interface TextInput {
  id: string;
  content: string;
  label?: string;
  metadata?: Record<string, any>;
}

interface AnalysisRequest {
  texts: TextInput[];
  options: {
    models: string[];
    threshold: number;
    enableCloneDetection: boolean;
    enableModelComparison: boolean;
    preprocessingOptions: {
      removeStopWords: boolean;
      stemming: boolean;
      lemmatization: boolean;
      removeNumbers: boolean;
      removePunctuation: boolean;
      toLowerCase: boolean;
      removeExtraWhitespace: boolean;
      minWordLength: number;
      customStopWords?: string[];
    };
  };
}

interface EmbeddingModel {
  name: string;
  displayName: string;
  provider: 'openai' | 'huggingface' | 'sentence-transformers';
  dimensions: number;
  maxTokens: number;
  costPerToken?: number;
  description: string;
  strengths: string[];
  limitations: string[];
  recommendedFor: string[];
}

interface AnalysisResponse {
  requestId: string;
  processedTexts: any[];
  embeddings: Record<string, any[]>;
  similarityMatrices: Record<string, any>;
  cloneDetectionResults: any[];
  modelComparison?: any;
  summary: {
    totalTexts: number;
    modelsUsed: string[];
    processingTime: number;
    clonesFound: number;
    averageSimilarity: number;
    recommendations: string[];
  };
  timestamp: Date;
}

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata?: {
    requestId: string;
    timestamp: Date;
    processingTime: number;
  };
}

export const useAnalysis = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState<AnalysisResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [availableModels, setAvailableModels] = useState<EmbeddingModel[] | null>(null);

  // Fetch available models on mount
  useEffect(() => {
    fetchAvailableModels();
  }, []);

  const fetchAvailableModels = useCallback(async () => {
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}/api/models`);
      const result: ApiResponse<EmbeddingModel[]> = await response.json();
      
      if (result.success && result.data) {
        setAvailableModels(result.data);
      } else {
        console.error('Failed to fetch models:', result.error);
      }
    } catch (err) {
      console.error('Error fetching models:', err);
    }
  }, []);

  const analyzeTexts = useCallback(async (request: AnalysisRequest) => {
    setIsLoading(true);
    setError(null);
    setData(null);

    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}/api/analyze`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      const result: ApiResponse<AnalysisResponse> = await response.json();

      if (result.success && result.data) {
        setData(result.data);
      } else {
        setError(result.error?.message || 'Analysis failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Network error occurred');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setIsLoading(false);
  }, []);

  return {
    analyzeTexts,
    isLoading,
    data,
    error,
    availableModels,
    reset,
  };
};
