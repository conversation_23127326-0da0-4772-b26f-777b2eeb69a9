import crypto from 'crypto';
import jwt from 'jsonwebtoken';
import { loggers } from '../utils/logger.js';
import { db } from '../database/models.js';
import { config, apiKeys } from '../config/index.js';
import {
  AuthenticationError,
  AuthorizationError,
  TenantConfig,
  ApiKey,
  Permission,
  AuditLogEntry
} from '../types/index.js';

/**
 * Comprehensive Authentication Manager
 * Handles API key authentication, permissions, and multi-tenancy
 */
export class AuthManager {
  private apiKeyCache: Map<string, ApiKey> = new Map();
  private tenantCache: Map<string, TenantConfig> = new Map();
  private cacheExpiry: Map<string, number> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  constructor() {
    this.initializeDefaultTenants();

    loggers.info('Authentication Manager initialized', {
      cacheEnabled: true,
      cacheTTL: this.CACHE_TTL
    });
  }

  /**
   * Initialize default tenants from configuration
   */
  private async initializeDefaultTenants(): Promise<void> {
    try {
      for (const [apiKey, tenantId] of apiKeys.entries()) {
        // Check if tenant exists in database
        let tenant = await db.getTenant(tenantId);

        if (!tenant) {
          // Create default tenant
          tenant = await this.createDefaultTenant(tenantId, apiKey);
        }

        // Cache the tenant
        this.tenantCache.set(tenantId, tenant);
        this.cacheExpiry.set(`tenant:${tenantId}`, Date.now() + this.CACHE_TTL);

        loggers.info('Default tenant initialized', { tenantId });
      }
    } catch (error) {
      loggers.error('Failed to initialize default tenants', error as Error);
    }
  }

  /**
   * Create a default tenant configuration
   */
  private async createDefaultTenant(tenantId: string, apiKey: string): Promise<TenantConfig> {
    const tenant: Omit<TenantConfig, 'createdAt' | 'updatedAt'> = {
      id: tenantId,
      name: `Default Tenant ${tenantId}`,
      discordBotToken: process.env.DISCORD_BOT_TOKEN || '',
      permissions: [
        {
          resource: 'discord',
          actions: ['send_message', 'get_messages', 'get_channel_info', 'search_messages', 'moderate_content']
        }
      ],
      quotas: {
        maxRequestsPerMinute: 60,
        maxRequestsPerHour: 1000,
        maxMessageHistory: 100,
        maxSearchResults: 50
      }
    };

    const createdTenant = await db.createTenant(tenant);

    // Create API key for this tenant
    await this.createApiKey({
      id: crypto.randomUUID(),
      key: this.hashApiKey(apiKey),
      tenantId: tenantId,
      name: 'Default API Key',
      permissions: tenant.permissions
    });

    return createdTenant;
  }

  /**
   * Validate an MCP request with API key authentication
   */
  async validateRequest(request: any, apiKey?: string): Promise<{ valid: boolean; tenantId?: string; permissions?: Permission[] }> {
    try {
      if (!apiKey) {
        // Try to extract API key from request headers or metadata
        apiKey = this.extractApiKeyFromRequest(request);
      }

      if (!apiKey) {
        loggers.warn('No API key provided in request');
        return { valid: false };
      }

      // Validate API key
      const keyData = await this.validateApiKey(apiKey);

      if (!keyData) {
        loggers.warn('Invalid API key provided');
        return { valid: false };
      }

      // Check if key is expired
      if (keyData.expiresAt && keyData.expiresAt < new Date()) {
        loggers.warn('Expired API key used', { keyId: keyData.id });
        return { valid: false };
      }

      // Update last used timestamp
      await db.updateApiKeyLastUsed(keyData.id);

      // Get tenant configuration
      const tenant = await this.getTenant(keyData.tenantId);

      if (!tenant) {
        loggers.error('Tenant not found for valid API key', { tenantId: keyData.tenantId });
        return { valid: false };
      }

      loggers.debug('Request authenticated successfully', {
        tenantId: keyData.tenantId,
        keyId: keyData.id
      });

      return {
        valid: true,
        tenantId: keyData.tenantId,
        permissions: keyData.permissions
      };

    } catch (error) {
      loggers.error('Authentication validation error', error as Error);
      return { valid: false };
    }
  }

  /**
   * Extract API key from MCP request
   */
  private extractApiKeyFromRequest(request: any): string | undefined {
    // Try different locations where API key might be stored
    // This depends on how the MCP client sends authentication

    // Check request metadata
    if (request.meta?.apiKey) {
      return request.meta.apiKey;
    }

    // Check request params
    if (request.params?.apiKey) {
      return request.params.apiKey;
    }

    // Check environment variable as fallback
    if (process.env.MCP_API_KEY) {
      return process.env.MCP_API_KEY;
    }

    return undefined;
  }

  /**
   * Validate API key and return key data
   */
  async validateApiKey(apiKey: string): Promise<ApiKey | null> {
    const hashedKey = this.hashApiKey(apiKey);

    // Check cache first
    const cacheKey = `apikey:${hashedKey}`;
    const cached = this.apiKeyCache.get(cacheKey);
    const cacheExpiry = this.cacheExpiry.get(cacheKey);

    if (cached && cacheExpiry && Date.now() < cacheExpiry) {
      return cached;
    }

    // Fetch from database
    const keyData = await db.getApiKeyByHash(hashedKey);

    if (keyData) {
      // Cache the result
      this.apiKeyCache.set(cacheKey, keyData);
      this.cacheExpiry.set(cacheKey, Date.now() + this.CACHE_TTL);
    }

    return keyData;
  }

  /**
   * Hash API key for secure storage
   */
  private hashApiKey(apiKey: string): string {
    return crypto.createHash('sha256')
      .update(apiKey + config.security.encryptionKey)
      .digest('hex');
  }

  /**
   * Get tenant configuration
   */
  async getTenant(tenantId: string): Promise<TenantConfig | null> {
    // Check cache first
    const cached = this.tenantCache.get(tenantId);
    const cacheExpiry = this.cacheExpiry.get(`tenant:${tenantId}`);

    if (cached && cacheExpiry && Date.now() < cacheExpiry) {
      return cached;
    }

    // Fetch from database
    const tenant = await db.getTenant(tenantId);

    if (tenant) {
      // Cache the result
      this.tenantCache.set(tenantId, tenant);
      this.cacheExpiry.set(`tenant:${tenantId}`, Date.now() + this.CACHE_TTL);
    }

    return tenant;
  }

  /**
   * Check if user has permission for a specific action
   */
  hasPermission(permissions: Permission[], resource: string, action: string): boolean {
    return permissions.some(permission =>
      permission.resource === resource &&
      permission.actions.includes(action)
    );
  }

  /**
   * Create a new API key
   */
  async createApiKey(keyData: Omit<ApiKey, 'createdAt'>): Promise<ApiKey> {
    const apiKey = await db.createApiKey(keyData);

    // Clear cache for this tenant
    this.clearTenantCache(keyData.tenantId);

    loggers.audit('API_KEY_CREATED', {
      keyId: apiKey.id,
      tenantId: apiKey.tenantId,
      name: apiKey.name
    });

    return apiKey;
  }

  /**
   * Create a new tenant
   */
  async createTenant(tenantData: Omit<TenantConfig, 'createdAt' | 'updatedAt'>): Promise<TenantConfig> {
    const tenant = await db.createTenant(tenantData);

    // Cache the new tenant
    this.tenantCache.set(tenant.id, tenant);
    this.cacheExpiry.set(`tenant:${tenant.id}`, Date.now() + this.CACHE_TTL);

    loggers.audit('TENANT_CREATED', {
      tenantId: tenant.id,
      name: tenant.name
    });

    return tenant;
  }

  /**
   * Clear cache for a specific tenant
   */
  private clearTenantCache(tenantId: string): void {
    this.tenantCache.delete(tenantId);
    this.cacheExpiry.delete(`tenant:${tenantId}`);

    // Clear related API key caches
    for (const [key, value] of this.apiKeyCache.entries()) {
      if (value.tenantId === tenantId) {
        this.apiKeyCache.delete(key);
        this.cacheExpiry.delete(key);
      }
    }
  }

  /**
   * Log audit event
   */
  async logAuditEvent(
    tenantId: string,
    action: string,
    resource: string,
    resourceId: string,
    details: Record<string, any>,
    userId?: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    const auditEntry: Omit<AuditLogEntry, 'timestamp'> = {
      id: crypto.randomUUID(),
      tenantId,
      userId,
      action,
      resource,
      resourceId,
      details,
      ipAddress,
      userAgent
    };

    await db.createAuditLog(auditEntry);

    loggers.audit(action, {
      tenantId,
      resource,
      resourceId,
      details
    });
  }

  /**
   * Get audit logs for a tenant
   */
  async getAuditLogs(tenantId: string, limit: number = 100, offset: number = 0): Promise<AuditLogEntry[]> {
    return await db.getAuditLogs(tenantId, limit, offset);
  }

  /**
   * Cleanup expired cache entries
   */
  cleanupCache(): void {
    const now = Date.now();

    for (const [key, expiry] of this.cacheExpiry.entries()) {
      if (now > expiry) {
        this.cacheExpiry.delete(key);

        if (key.startsWith('tenant:')) {
          const tenantId = key.replace('tenant:', '');
          this.tenantCache.delete(tenantId);
        } else if (key.startsWith('apikey:')) {
          this.apiKeyCache.delete(key);
        }
      }
    }
  }
}
