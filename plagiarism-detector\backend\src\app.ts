import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { AnalysisController } from './controllers/analysisController.js';
import { corsConfig, rateLimitConfig } from './utils/config.js';

/**
 * Express Application Setup
 * Main server configuration with middleware and routes
 */
export class App {
  public app: express.Application;
  private analysisController: AnalysisController;

  constructor() {
    this.app = express();
    this.analysisController = new AnalysisController();
    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  /**
   * Initialize middleware
   */
  private initializeMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));

    // CORS configuration
    this.app.use(cors(corsConfig));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: rateLimitConfig.windowMs,
      max: rateLimitConfig.max,
      message: {
        success: false,
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: 'Too many requests, please try again later',
        },
      },
      standardHeaders: true,
      legacyHeaders: false,
    });
    this.app.use('/api/', limiter);

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging
    this.app.use((req, res, next) => {
      console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
      next();
    });
  }

  /**
   * Initialize routes
   */
  private initializeRoutes(): void {
    // Health check route
    this.app.get('/health', this.analysisController.healthCheck.bind(this.analysisController));

    // API routes
    const apiRouter = express.Router();

    // Analysis endpoints
    apiRouter.post('/analyze', this.analysisController.analyzeTexts.bind(this.analysisController));
    apiRouter.get('/models', this.analysisController.getAvailableModels.bind(this.analysisController));

    // Mount API router
    this.app.use('/api', apiRouter);

    // Root route
    this.app.get('/', (req, res) => {
      res.json({
        success: true,
        message: 'Plagiarism Detection API',
        version: '1.0.0',
        endpoints: {
          health: '/health',
          analyze: '/api/analyze',
          models: '/api/models',
        },
        documentation: 'https://github.com/your-repo/plagiarism-detector',
      });
    });

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: `Route ${req.originalUrl} not found`,
        },
      });
    });
  }

  /**
   * Initialize error handling
   */
  private initializeErrorHandling(): void {
    // Global error handler
    this.app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
      console.error('Unhandled error:', error);

      // Don't send error details in production
      const isDevelopment = process.env.NODE_ENV === 'development';

      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An unexpected error occurred',
          ...(isDevelopment && { details: error.message, stack: error.stack }),
        },
        metadata: {
          timestamp: new Date(),
          requestId: req.headers['x-request-id'] || 'unknown',
        },
      });
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      console.error('Uncaught Exception:', error);
      process.exit(1);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
      process.exit(1);
    });
  }

  /**
   * Start the server
   */
  public listen(port: number): void {
    this.app.listen(port, () => {
      console.log(`🚀 Plagiarism Detection API server running on port ${port}`);
      console.log(`📊 Health check: http://localhost:${port}/health`);
      console.log(`🔍 Analysis endpoint: http://localhost:${port}/api/analyze`);
      console.log(`📋 Available models: http://localhost:${port}/api/models`);
    });
  }
}

// Create and export app instance
const app = new App();
export default app;
