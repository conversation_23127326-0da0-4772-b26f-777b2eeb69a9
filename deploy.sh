#!/bin/bash

# Plagiarism Detector Deployment Script
# This script automates the deployment process for both development and production environments

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="plagiarism-detector"
BACKEND_DIR="plagiarism-detector/backend"
FRONTEND_DIR="plagiarism-detector/frontend"
LOG_FILE="deployment.log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a $LOG_FILE
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a $LOG_FILE
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a $LOG_FILE
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a $LOG_FILE
    exit 1
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js is not installed. Please install Node.js 18 or higher."
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        error "Node.js version 18 or higher is required. Current version: $(node --version)"
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        error "npm is not installed."
    fi
    
    success "Prerequisites check passed"
}

# Install dependencies
install_dependencies() {
    log "Installing dependencies..."
    
    # Backend dependencies
    log "Installing backend dependencies..."
    cd $BACKEND_DIR
    npm ci
    cd - > /dev/null
    
    # Frontend dependencies
    log "Installing frontend dependencies..."
    cd $FRONTEND_DIR
    npm ci
    cd - > /dev/null
    
    success "Dependencies installed successfully"
}

# Build applications
build_applications() {
    log "Building applications..."
    
    # Build backend
    log "Building backend..."
    cd $BACKEND_DIR
    npm run build
    cd - > /dev/null
    
    # Build frontend
    log "Building frontend..."
    cd $FRONTEND_DIR
    npm run build
    cd - > /dev/null
    
    success "Applications built successfully"
}

# Run tests
run_tests() {
    log "Running tests..."
    
    # Backend tests
    log "Running backend tests..."
    cd $BACKEND_DIR
    npm test || warning "Some backend tests failed"
    cd - > /dev/null
    
    # Frontend tests
    log "Running frontend tests..."
    cd $FRONTEND_DIR
    npm test run || warning "Some frontend tests failed"
    cd - > /dev/null
    
    success "Tests completed"
}

# Environment setup
setup_environment() {
    log "Setting up environment..."
    
    # Check for environment files
    if [ ! -f "$BACKEND_DIR/.env" ]; then
        warning "Backend .env file not found. Please create one based on .env.example"
    fi
    
    if [ ! -f "$FRONTEND_DIR/.env.production" ]; then
        warning "Frontend .env.production file not found. Please create one based on .env.example"
    fi
    
    # Create logs directory
    mkdir -p $BACKEND_DIR/logs
    
    success "Environment setup completed"
}

# Docker deployment
deploy_docker() {
    log "Deploying with Docker..."
    
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed."
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed."
    fi
    
    # Build and start containers
    docker-compose down || true
    docker-compose build
    docker-compose up -d
    
    # Wait for services to be healthy
    log "Waiting for services to be healthy..."
    sleep 30
    
    # Check health
    if docker-compose ps | grep -q "Up (healthy)"; then
        success "Docker deployment completed successfully"
    else
        error "Docker deployment failed - services are not healthy"
    fi
}

# PM2 deployment
deploy_pm2() {
    log "Deploying with PM2..."
    
    if ! command -v pm2 &> /dev/null; then
        log "Installing PM2..."
        npm install -g pm2
    fi
    
    # Start backend with PM2
    cd $BACKEND_DIR
    pm2 start ecosystem.config.js --env production
    cd - > /dev/null
    
    # Setup frontend with nginx or serve
    if command -v nginx &> /dev/null; then
        log "Setting up nginx for frontend..."
        sudo cp $FRONTEND_DIR/nginx.conf /etc/nginx/sites-available/$PROJECT_NAME
        sudo ln -sf /etc/nginx/sites-available/$PROJECT_NAME /etc/nginx/sites-enabled/
        sudo cp -r $FRONTEND_DIR/dist/* /var/www/html/
        sudo nginx -t && sudo systemctl reload nginx
    else
        log "Starting frontend with serve..."
        npm install -g serve
        pm2 start "serve -s $FRONTEND_DIR/dist -l 3000" --name "$PROJECT_NAME-frontend"
    fi
    
    success "PM2 deployment completed successfully"
}

# Health check
health_check() {
    log "Performing health check..."
    
    # Check backend
    if curl -f http://localhost:5000/health > /dev/null 2>&1; then
        success "Backend health check passed"
    else
        error "Backend health check failed"
    fi
    
    # Check frontend
    if curl -f http://localhost:3000 > /dev/null 2>&1 || curl -f http://localhost:80 > /dev/null 2>&1; then
        success "Frontend health check passed"
    else
        error "Frontend health check failed"
    fi
}

# Cleanup
cleanup() {
    log "Cleaning up..."
    
    # Remove temporary files
    find . -name "*.tmp" -delete 2>/dev/null || true
    find . -name "*.log" -not -name "$LOG_FILE" -delete 2>/dev/null || true
    
    success "Cleanup completed"
}

# Main deployment function
deploy() {
    local deployment_type=${1:-"pm2"}
    
    log "Starting deployment process..."
    log "Deployment type: $deployment_type"
    
    check_prerequisites
    setup_environment
    install_dependencies
    build_applications
    run_tests
    
    case $deployment_type in
        "docker")
            deploy_docker
            ;;
        "pm2")
            deploy_pm2
            ;;
        *)
            error "Unknown deployment type: $deployment_type. Use 'docker' or 'pm2'"
            ;;
    esac
    
    health_check
    cleanup
    
    success "Deployment completed successfully!"
    log "Application is now running:"
    log "  - Backend: http://localhost:5000"
    log "  - Frontend: http://localhost:3000 (or http://localhost:80 for docker)"
    log "  - Health Check: http://localhost:5000/health"
}

# Script usage
usage() {
    echo "Usage: $0 [deployment_type]"
    echo "  deployment_type: 'docker' or 'pm2' (default: pm2)"
    echo ""
    echo "Examples:"
    echo "  $0              # Deploy with PM2"
    echo "  $0 pm2          # Deploy with PM2"
    echo "  $0 docker       # Deploy with Docker"
}

# Main script execution
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    usage
    exit 0
fi

# Start deployment
deploy $1
