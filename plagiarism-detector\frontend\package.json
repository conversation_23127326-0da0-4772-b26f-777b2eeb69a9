{"name": "plagiarism-detector-frontend", "version": "1.0.0", "description": "React Frontend for Plagiarism Detection Application", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "axios": "^1.6.2", "@tanstack/react-query": "^5.8.4", "react-router-dom": "^6.20.1", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "recharts": "^2.8.0", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.5", "three": "^0.158.0", "@react-three/fiber": "^8.15.11", "@react-three/drei": "^9.88.13", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/three": "^0.158.3", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^5.0.0", "vitest": "^1.0.0", "@vitest/ui": "^1.0.0", "@vitest/coverage-v8": "^1.0.0", "jsdom": "^23.0.1", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1"}, "engines": {"node": ">=18.0.0"}}