import dotenv from 'dotenv';
import { App } from './app.js';
import { serverConfig } from './utils/config.js';

// Load environment variables
dotenv.config();

/**
 * Server Entry Point
 * Initialize and start the plagiarism detection API server
 */
async function startServer(): Promise<void> {
  try {
    console.log('🔧 Initializing Plagiarism Detection API...');
    
    // Validate required environment variables
    const requiredEnvVars = ['OPENAI_API_KEY'];
    const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
    
    if (missingEnvVars.length > 0) {
      console.error('❌ Missing required environment variables:', missingEnvVars);
      console.error('Please check your .env file and ensure all required variables are set.');
      process.exit(1);
    }

    // Create and start the application
    const app = new App();
    const port = serverConfig.port;

    console.log('✅ Configuration loaded successfully');
    console.log(`📡 Starting server on port ${port}...`);
    
    app.listen(port);

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully...');
  process.exit(0);
});

// Start the server
startServer().catch((error) => {
  console.error('❌ Server startup failed:', error);
  process.exit(1);
});
