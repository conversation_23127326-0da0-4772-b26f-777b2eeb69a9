import { TextPreprocessorService } from '../../services/textPreprocessor';
import { PreprocessingOptions, TextInput } from '../../types';

describe('TextPreprocessorService', () => {
  let preprocessor: TextPreprocessorService;

  beforeEach(() => {
    preprocessor = new TextPreprocessorService();
  });

  describe('processText', () => {
    it('should handle basic text preprocessing', async () => {
      const textInput: TextInput = {
        content: 'Hello World! This is a TEST.',
        label: 'Test Document'
      };
      const options: PreprocessingOptions = {
        removeStopWords: false,
        stemming: false,
        lemmatization: false,
        removeNumbers: false,
        removePunctuation: false,
        toLowerCase: true,
        removeExtraWhitespace: true,
        minWordLength: 1,
      };

      const result = await preprocessor.processText(textInput, options);

      expect(result.originalText).toBe(textInput.content);
      expect(result.processedText).toBe('hello world! this is a test.');
      expect(result.wordCount).toBeGreaterThan(0);
      expect(result.characterCount).toBeGreaterThan(0);
    });

    it('should remove punctuation when enabled', async () => {
      const text = 'Hello, World! How are you?';
      const options: PreprocessingOptions = {
        removeStopWords: false,
        stemming: false,
        lemmatization: false,
        removeNumbers: false,
        removePunctuation: true,
        toLowerCase: true,
        removeExtraWhitespace: true,
        minWordLength: 1,
      };

      const result = await preprocessor.preprocessText(text, options);

      expect(result.processedText).toBe('hello world how are you');
      expect(result.processedText).not.toContain(',');
      expect(result.processedText).not.toContain('!');
      expect(result.processedText).not.toContain('?');
    });

    it('should remove numbers when enabled', async () => {
      const text = 'There are 123 items and 456 more.';
      const options: PreprocessingOptions = {
        removeStopWords: false,
        stemming: false,
        lemmatization: false,
        removeNumbers: true,
        removePunctuation: false,
        toLowerCase: true,
        removeExtraWhitespace: true,
        minWordLength: 1,
      };

      const result = await preprocessor.preprocessText(text, options);

      expect(result.processedText).not.toContain('123');
      expect(result.processedText).not.toContain('456');
      expect(result.processedText).toContain('there are');
      expect(result.processedText).toContain('items and');
    });

    it('should filter words by minimum length', async () => {
      const text = 'I am a big elephant';
      const options: PreprocessingOptions = {
        removeStopWords: false,
        stemming: false,
        lemmatization: false,
        removeNumbers: false,
        removePunctuation: false,
        toLowerCase: true,
        removeExtraWhitespace: true,
        minWordLength: 3,
      };

      const result = await preprocessor.preprocessText(text, options);

      expect(result.processedText).not.toContain(' i ');
      expect(result.processedText).not.toContain(' am ');
      expect(result.processedText).toContain('big');
      expect(result.processedText).toContain('elephant');
    });

    it('should remove extra whitespace', async () => {
      const text = 'Hello    world   with   extra    spaces';
      const options: PreprocessingOptions = {
        removeStopWords: false,
        stemming: false,
        lemmatization: false,
        removeNumbers: false,
        removePunctuation: false,
        toLowerCase: false,
        removeExtraWhitespace: true,
        minWordLength: 1,
      };

      const result = await preprocessor.preprocessText(text, options);

      expect(result.processedText).toBe('Hello world with extra spaces');
      expect(result.processedText).not.toContain('  ');
    });

    it('should handle empty text', async () => {
      const text = '';
      const options: PreprocessingOptions = {
        removeStopWords: false,
        stemming: false,
        lemmatization: false,
        removeNumbers: false,
        removePunctuation: false,
        toLowerCase: true,
        removeExtraWhitespace: true,
        minWordLength: 1,
      };

      const result = await preprocessor.preprocessText(text, options);

      expect(result.originalText).toBe('');
      expect(result.processedText).toBe('');
      expect(result.wordCount).toBe(0);
      expect(result.characterCount).toBe(0);
    });

    it('should handle text with only whitespace', async () => {
      const text = '   \n\t   ';
      const options: PreprocessingOptions = {
        removeStopWords: false,
        stemming: false,
        lemmatization: false,
        removeNumbers: false,
        removePunctuation: false,
        toLowerCase: true,
        removeExtraWhitespace: true,
        minWordLength: 1,
      };

      const result = await preprocessor.preprocessText(text, options);

      expect(result.processedText).toBe('');
      expect(result.wordCount).toBe(0);
    });
  });

  describe('validateText', () => {
    it('should validate text length', () => {
      const shortText = 'Hi';
      const validText = 'This is a valid text for processing';
      const longText = 'x'.repeat(10001);

      expect(() => preprocessor.validateText(shortText)).toThrow('Text is too short');
      expect(() => preprocessor.validateText(validText)).not.toThrow();
      expect(() => preprocessor.validateText(longText)).toThrow('Text is too long');
    });

    it('should reject empty or whitespace-only text', () => {
      expect(() => preprocessor.validateText('')).toThrow('Text cannot be empty');
      expect(() => preprocessor.validateText('   ')).toThrow('Text cannot be empty');
      expect(() => preprocessor.validateText('\n\t')).toThrow('Text cannot be empty');
    });
  });

  describe('tokenize', () => {
    it('should tokenize text into words', () => {
      const text = 'Hello world, how are you?';
      const tokens = preprocessor.tokenize(text);

      expect(tokens).toContain('Hello');
      expect(tokens).toContain('world');
      expect(tokens).toContain('how');
      expect(tokens).toContain('are');
      expect(tokens).toContain('you');
      expect(tokens.length).toBeGreaterThan(0);
    });

    it('should handle punctuation in tokenization', () => {
      const text = "Don't split contractions, but split sentences.";
      const tokens = preprocessor.tokenize(text);

      expect(tokens).toContain("Don't");
      expect(tokens).toContain('split');
      expect(tokens).toContain('contractions');
      expect(tokens).toContain('but');
      expect(tokens).toContain('sentences');
    });
  });

  describe('removeStopWords', () => {
    it('should remove common English stop words', () => {
      const tokens = ['the', 'quick', 'brown', 'fox', 'is', 'jumping'];
      const filtered = preprocessor.removeStopWords(tokens);

      expect(filtered).not.toContain('the');
      expect(filtered).not.toContain('is');
      expect(filtered).toContain('quick');
      expect(filtered).toContain('brown');
      expect(filtered).toContain('fox');
      expect(filtered).toContain('jumping');
    });

    it('should handle custom stop words', () => {
      const tokens = ['hello', 'world', 'custom', 'word'];
      const customStopWords = ['custom'];
      const filtered = preprocessor.removeStopWords(tokens, customStopWords);

      expect(filtered).not.toContain('custom');
      expect(filtered).toContain('hello');
      expect(filtered).toContain('world');
      expect(filtered).toContain('word');
    });
  });

  describe('calculateStatistics', () => {
    it('should calculate text statistics correctly', () => {
      const originalText = 'Hello world! This is a test.';
      const processedText = 'hello world this is a test';
      
      const stats = preprocessor.calculateStatistics(originalText, processedText);

      expect(stats.originalLength).toBe(originalText.length);
      expect(stats.processedLength).toBe(processedText.length);
      expect(stats.wordCount).toBeGreaterThan(0);
      expect(stats.characterCount).toBeGreaterThan(0);
      expect(stats.reductionPercentage).toBeGreaterThanOrEqual(0);
      expect(stats.reductionPercentage).toBeLessThanOrEqual(100);
    });

    it('should handle identical original and processed text', () => {
      const text = 'unchanged text';
      const stats = preprocessor.calculateStatistics(text, text);

      expect(stats.originalLength).toBe(stats.processedLength);
      expect(stats.reductionPercentage).toBe(0);
    });
  });

  describe('error handling', () => {
    it('should handle invalid preprocessing options gracefully', async () => {
      const text = 'Valid text for testing';
      const invalidOptions = {
        minWordLength: -1, // Invalid negative value
      } as PreprocessingOptions;

      // Should not throw, but handle gracefully
      const result = await preprocessor.preprocessText(text, invalidOptions);
      expect(result.originalText).toBe(text);
    });

    it('should handle very long text processing', async () => {
      const longText = 'word '.repeat(1000).trim();
      const options: PreprocessingOptions = {
        removeStopWords: false,
        stemming: false,
        lemmatization: false,
        removeNumbers: false,
        removePunctuation: false,
        toLowerCase: true,
        removeExtraWhitespace: true,
        minWordLength: 1,
      };

      const result = await preprocessor.preprocessText(longText, options);
      expect(result.wordCount).toBe(1000);
      expect(result.processedText.length).toBeGreaterThan(0);
    });
  });
});
