import { Client, TextChannel, DMChannel, NewsChannel, ThreadChannel, Message } from 'discord.js';
import { z } from 'zod';
import { loggers } from '../utils/logger.js';
import { DiscordAPIError, ValidationError, DiscordMessage } from '../types/index.js';
import { performanceConfig } from '../config/index.js';

// Input schema for get_messages tool
const GetMessagesInput = z.object({
  channel_id: z.string().min(1, "Channel ID is required"),
  limit: z.number().min(1).max(100).default(50),
  before: z.string().optional(),
  after: z.string().optional()
});

type GetMessagesArgs = z.infer<typeof GetMessagesInput>;

/**
 * Retrieve message history from a Discord channel
 */
export async function getMessages(client: Client, args: GetMessagesArgs) {
  const startTime = Date.now();
  
  try {
    loggers.info('Fetching Discord messages', {
      channelId: args.channel_id,
      limit: args.limit,
      before: args.before,
      after: args.after
    });

    // Get the channel
    const channel = await client.channels.fetch(args.channel_id);
    
    if (!channel) {
      throw new ValidationError(`Channel not found: ${args.channel_id}`);
    }

    // Check if channel is a text-based channel
    if (!isTextBasedChannel(channel)) {
      throw new ValidationError(`Channel ${args.channel_id} is not a text-based channel`);
    }

    // Prepare fetch options
    const fetchOptions: any = {
      limit: Math.min(args.limit, performanceConfig.maxMessageHistory)
    };

    if (args.before) {
      fetchOptions.before = args.before;
    }

    if (args.after) {
      fetchOptions.after = args.after;
    }

    // Fetch messages
    const messages = await channel.messages.fetch(fetchOptions);
    
    // Convert Discord messages to our format
    const formattedMessages: DiscordMessage[] = messages.map(message => 
      formatDiscordMessage(message)
    );

    // Sort by timestamp (newest first)
    formattedMessages.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );

    const duration = Date.now() - startTime;
    
    loggers.info('Discord messages fetched successfully', {
      channelId: args.channel_id,
      messageCount: formattedMessages.length,
      duration
    });

    // Return response
    return {
      success: true,
      channel: {
        id: channel.id,
        name: 'name' in channel ? channel.name : 'DM',
        type: channel.type
      },
      messages: formattedMessages,
      pagination: {
        requested_limit: args.limit,
        actual_count: formattedMessages.length,
        has_more: messages.size === fetchOptions.limit,
        before: args.before,
        after: args.after,
        oldest_message_id: formattedMessages.length > 0 ? 
          formattedMessages[formattedMessages.length - 1].id : null,
        newest_message_id: formattedMessages.length > 0 ? 
          formattedMessages[0].id : null
      }
    };

  } catch (error) {
    const duration = Date.now() - startTime;
    
    loggers.error('Failed to fetch Discord messages', error as Error, {
      channelId: args.channel_id,
      duration
    });

    // Handle specific Discord API errors
    if (error instanceof Error) {
      if (error.message.includes('Missing Permissions')) {
        throw new DiscordAPIError('Bot lacks permission to read messages in this channel');
      }
      
      if (error.message.includes('Unknown Channel')) {
        throw new ValidationError(`Channel not found or bot cannot access: ${args.channel_id}`);
      }
      
      if (error.message.includes('Invalid Form Body')) {
        throw new ValidationError('Invalid message ID provided in before/after parameter');
      }
    }

    // Re-throw validation errors as-is
    if (error instanceof ValidationError) {
      throw error;
    }

    // Wrap other errors
    throw new DiscordAPIError(`Failed to fetch messages: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Format a Discord message to our standard format
 */
function formatDiscordMessage(message: Message): DiscordMessage {
  return {
    id: message.id,
    content: message.content,
    author: {
      id: message.author.id,
      username: message.author.username,
      discriminator: message.author.discriminator
    },
    channel_id: message.channelId,
    guild_id: message.guildId || undefined,
    timestamp: message.createdAt.toISOString(),
    edited_timestamp: message.editedAt?.toISOString(),
    embeds: message.embeds.map(embed => ({
      title: embed.title || undefined,
      description: embed.description || undefined,
      color: embed.color || undefined,
      fields: embed.fields.map(field => ({
        name: field.name,
        value: field.value,
        inline: field.inline
      })),
      footer: embed.footer ? {
        text: embed.footer.text,
        icon_url: embed.footer.iconURL || undefined
      } : undefined,
      timestamp: embed.timestamp || undefined
    }))
  };
}

/**
 * Type guard to check if channel supports reading messages
 */
function isTextBasedChannel(channel: any): channel is TextChannel | DMChannel | NewsChannel | ThreadChannel {
  return channel && typeof channel.messages?.fetch === 'function';
}

/**
 * Get message statistics for a channel
 */
export async function getMessageStats(client: Client, channelId: string) {
  try {
    const channel = await client.channels.fetch(channelId);
    
    if (!channel || !isTextBasedChannel(channel)) {
      throw new ValidationError('Invalid channel for message statistics');
    }

    // Fetch recent messages for stats
    const recentMessages = await channel.messages.fetch({ limit: 100 });
    
    const stats = {
      total_fetched: recentMessages.size,
      unique_authors: new Set(recentMessages.map(m => m.author.id)).size,
      messages_with_embeds: recentMessages.filter(m => m.embeds.length > 0).size,
      messages_with_attachments: recentMessages.filter(m => m.attachments.size > 0).size,
      average_message_length: recentMessages.size > 0 ? 
        recentMessages.reduce((sum, m) => sum + m.content.length, 0) / recentMessages.size : 0,
      date_range: {
        oldest: recentMessages.size > 0 ? 
          Math.min(...recentMessages.map(m => m.createdTimestamp)) : null,
        newest: recentMessages.size > 0 ? 
          Math.max(...recentMessages.map(m => m.createdTimestamp)) : null
      }
    };

    return stats;
    
  } catch (error) {
    loggers.error('Failed to get message statistics', error as Error, { channelId });
    throw new DiscordAPIError('Failed to retrieve message statistics');
  }
}
