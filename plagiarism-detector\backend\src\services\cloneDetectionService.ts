import natural from 'natural';
import { ProcessedText, SimilarityMatrix, CloneDetectionResult, ConfidenceLevel } from '../types/index.js';

/**
 * Clone Detection Service
 * Advanced plagiarism detection using multiple similarity metrics and evidence gathering
 */
export class CloneDetectionService {
  private nGramAnalyzer = natural.NGrams;
  private jaccardDistance = natural.JaccardDistance;

  /**
   * Detect potential clones based on similarity threshold and additional evidence
   */
  detectClones(
    processedTexts: ProcessedText[],
    similarityMatrix: SimilarityMatrix,
    threshold: number = 0.8
  ): CloneDetectionResult[] {
    const clones: CloneDetectionResult[] = [];
    const { textIds, matrix, model } = similarityMatrix;

    // Create a map for quick text lookup
    const textMap = new Map<string, ProcessedText>();
    processedTexts.forEach(text => textMap.set(text.id, text));

    for (let i = 0; i < matrix.length; i++) {
      for (let j = i + 1; j < matrix[i].length; j++) {
        const similarity = matrix[i][j];
        
        if (similarity >= threshold) {
          const textId1 = textIds[i];
          const textId2 = textIds[j];
          const text1 = textMap.get(textId1);
          const text2 = textMap.get(textId2);

          if (text1 && text2) {
            const cloneResult = this.analyzeClonePair(
              text1,
              text2,
              similarity,
              model,
              threshold
            );
            clones.push(cloneResult);
          }
        }
      }
    }

    // Sort by similarity (highest first)
    return clones.sort((a, b) => b.similarity - a.similarity);
  }

  /**
   * Analyze a pair of texts for clone evidence
   */
  private analyzeClonePair(
    text1: ProcessedText,
    text2: ProcessedText,
    semanticSimilarity: number,
    model: string,
    threshold: number
  ): CloneDetectionResult {
    // Calculate additional similarity metrics
    const structuralSimilarity = this.calculateStructuralSimilarity(text1, text2);
    const commonPhrases = this.findCommonPhrases(text1, text2);
    const confidence = this.calculateConfidence(
      semanticSimilarity,
      structuralSimilarity,
      commonPhrases.length,
      threshold
    );

    return {
      textId1: text1.id,
      textId2: text2.id,
      similarity: semanticSimilarity,
      model,
      confidence,
      evidence: {
        commonPhrases,
        structuralSimilarity,
        semanticSimilarity,
      },
    };
  }

  /**
   * Calculate structural similarity between two texts
   */
  private calculateStructuralSimilarity(text1: ProcessedText, text2: ProcessedText): number {
    // Compare text structure metrics
    const lengthRatio = Math.min(
      text1.metadata.processedLength / text2.metadata.processedLength,
      text2.metadata.processedLength / text1.metadata.processedLength
    );

    const wordCountRatio = Math.min(
      text1.metadata.wordCount / text2.metadata.wordCount,
      text2.metadata.wordCount / text1.metadata.wordCount
    );

    const sentenceCountRatio = Math.min(
      text1.metadata.sentenceCount / text2.metadata.sentenceCount,
      text2.metadata.sentenceCount / text1.metadata.sentenceCount
    );

    // Calculate Jaccard similarity for word sets
    const words1 = new Set(text1.processedContent.toLowerCase().split(/\s+/));
    const words2 = new Set(text2.processedContent.toLowerCase().split(/\s+/));
    const jaccardSimilarity = 1 - this.jaccardDistance(words1, words2);

    // Weighted average of structural metrics
    return (
      lengthRatio * 0.2 +
      wordCountRatio * 0.2 +
      sentenceCountRatio * 0.2 +
      jaccardSimilarity * 0.4
    );
  }

  /**
   * Find common phrases between two texts
   */
  private findCommonPhrases(text1: ProcessedText, text2: ProcessedText): string[] {
    const commonPhrases: string[] = [];
    const minPhraseLength = 3; // Minimum words in a phrase
    const maxPhraseLength = 8; // Maximum words in a phrase

    // Generate n-grams for both texts
    for (let n = minPhraseLength; n <= maxPhraseLength; n++) {
      const ngrams1 = this.nGramAnalyzer.ngrams(
        text1.processedContent.toLowerCase().split(/\s+/),
        n
      );
      const ngrams2 = this.nGramAnalyzer.ngrams(
        text2.processedContent.toLowerCase().split(/\s+/),
        n
      );

      // Convert to strings for comparison
      const phrases1 = new Set(ngrams1.map(gram => gram.join(' ')));
      const phrases2 = new Set(ngrams2.map(gram => gram.join(' ')));

      // Find common phrases
      for (const phrase of phrases1) {
        if (phrases2.has(phrase) && phrase.length > 10) { // Only significant phrases
          commonPhrases.push(phrase);
        }
      }
    }

    // Remove duplicates and sort by length (longest first)
    return [...new Set(commonPhrases)]
      .sort((a, b) => b.length - a.length)
      .slice(0, 10); // Limit to top 10 phrases
  }

  /**
   * Calculate confidence level for clone detection
   */
  private calculateConfidence(
    semanticSimilarity: number,
    structuralSimilarity: number,
    commonPhrasesCount: number,
    threshold: number
  ): ConfidenceLevel {
    // Calculate composite score
    const compositeScore = (
      semanticSimilarity * 0.5 +
      structuralSimilarity * 0.3 +
      Math.min(commonPhrasesCount / 10, 1) * 0.2
    );

    // Determine confidence level
    if (compositeScore >= 0.95) {
      return 'very_high';
    } else if (compositeScore >= 0.85) {
      return 'high';
    } else if (compositeScore >= threshold) {
      return 'medium';
    } else {
      return 'low';
    }
  }

  /**
   * Detect different types of plagiarism
   */
  classifyPlagiarismType(cloneResult: CloneDetectionResult): {
    type: 'exact_copy' | 'paraphrase' | 'structural' | 'mosaic' | 'idea_plagiarism';
    description: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
  } {
    const { similarity, evidence } = cloneResult;
    const { semanticSimilarity, structuralSimilarity, commonPhrases } = evidence;

    // Exact copy detection
    if (semanticSimilarity > 0.98 && structuralSimilarity > 0.95 && commonPhrases.length > 5) {
      return {
        type: 'exact_copy',
        description: 'Nearly identical text with minimal or no changes',
        severity: 'critical',
      };
    }

    // Paraphrase detection
    if (semanticSimilarity > 0.85 && structuralSimilarity < 0.7 && commonPhrases.length < 3) {
      return {
        type: 'paraphrase',
        description: 'Same ideas expressed with different words and structure',
        severity: 'high',
      };
    }

    // Structural plagiarism
    if (structuralSimilarity > 0.8 && semanticSimilarity > 0.7) {
      return {
        type: 'structural',
        description: 'Similar structure and organization with some content changes',
        severity: 'high',
      };
    }

    // Mosaic plagiarism
    if (commonPhrases.length > 3 && semanticSimilarity > 0.75) {
      return {
        type: 'mosaic',
        description: 'Combination of copied phrases and paraphrased content',
        severity: 'medium',
      };
    }

    // Idea plagiarism
    if (semanticSimilarity > 0.6 && structuralSimilarity < 0.5) {
      return {
        type: 'idea_plagiarism',
        description: 'Similar concepts and ideas with different expression',
        severity: 'low',
      };
    }

    // Default classification
    return {
      type: 'paraphrase',
      description: 'Potential similarity detected',
      severity: 'medium',
    };
  }

  /**
   * Generate detailed clone detection report
   */
  generateCloneReport(
    cloneResults: CloneDetectionResult[],
    processedTexts: ProcessedText[]
  ): {
    summary: {
      totalClones: number;
      highConfidenceClones: number;
      averageSimilarity: number;
      mostSimilarPair: { textId1: string; textId2: string; similarity: number } | null;
    };
    detailedResults: Array<CloneDetectionResult & {
      plagiarismType: ReturnType<typeof this.classifyPlagiarismType>;
      textLabels: { text1Label?: string; text2Label?: string };
    }>;
    recommendations: string[];
  } {
    const textMap = new Map<string, ProcessedText>();
    processedTexts.forEach(text => textMap.set(text.id, text));

    const detailedResults = cloneResults.map(clone => {
      const text1 = textMap.get(clone.textId1);
      const text2 = textMap.get(clone.textId2);
      
      return {
        ...clone,
        plagiarismType: this.classifyPlagiarismType(clone),
        textLabels: {
          text1Label: text1?.label,
          text2Label: text2?.label,
        },
      };
    });

    const highConfidenceClones = cloneResults.filter(
      clone => clone.confidence === 'high' || clone.confidence === 'very_high'
    ).length;

    const averageSimilarity = cloneResults.length > 0
      ? cloneResults.reduce((sum, clone) => sum + clone.similarity, 0) / cloneResults.length
      : 0;

    const mostSimilarPair = cloneResults.length > 0
      ? {
          textId1: cloneResults[0].textId1,
          textId2: cloneResults[0].textId2,
          similarity: cloneResults[0].similarity,
        }
      : null;

    const recommendations = this.generateRecommendations(detailedResults);

    return {
      summary: {
        totalClones: cloneResults.length,
        highConfidenceClones,
        averageSimilarity,
        mostSimilarPair,
      },
      detailedResults,
      recommendations,
    };
  }

  /**
   * Generate recommendations based on clone detection results
   */
  private generateRecommendations(
    detailedResults: Array<CloneDetectionResult & { plagiarismType: any }>
  ): string[] {
    const recommendations: string[] = [];

    const criticalCount = detailedResults.filter(r => r.plagiarismType.severity === 'critical').length;
    const highCount = detailedResults.filter(r => r.plagiarismType.severity === 'high').length;

    if (criticalCount > 0) {
      recommendations.push(
        `Found ${criticalCount} critical similarity case(s). These require immediate attention as they indicate potential exact copying.`
      );
    }

    if (highCount > 0) {
      recommendations.push(
        `Found ${highCount} high similarity case(s). Review these for potential paraphrasing or structural plagiarism.`
      );
    }

    if (detailedResults.length > 5) {
      recommendations.push(
        'Multiple similarity cases detected. Consider reviewing the originality of the content collection.'
      );
    }

    const exactCopies = detailedResults.filter(r => r.plagiarismType.type === 'exact_copy').length;
    if (exactCopies > 0) {
      recommendations.push(
        `${exactCopies} exact or near-exact copies detected. These should be flagged for review.`
      );
    }

    if (recommendations.length === 0) {
      recommendations.push('No significant plagiarism concerns detected in the analyzed texts.');
    }

    return recommendations;
  }
}
