# Discord MCP Server API Documentation

## Overview

The Discord MCP Server provides 5 core tools for interacting with Discord through the Model Context Protocol. All tools require proper authentication and follow Discord's API guidelines.

## Authentication

All requests must include a valid API key in the request metadata:

```json
{
  "meta": {
    "apiKey": "your_api_key_here"
  }
}
```

## Rate Limits

- **Default**: 100 requests per minute per tenant
- **Configurable**: Can be adjusted per tenant in database
- **Headers**: Rate limit information included in responses

## Tools

### 1. send_message

Send a message to a Discord channel with optional embed.

**Parameters:**
- `channel_id` (string, required): Discord channel ID (17-19 digits)
- `content` (string, required): Message content (1-2000 characters)
- `embed` (object, optional): Rich embed object

**Embed Object:**
- `title` (string, optional): Embed title (max 256 chars)
- `description` (string, optional): Embed description (max 4096 chars)
- `color` (number, optional): Embed color (0-16777215)
- `fields` (array, optional): Array of field objects (max 25)
  - `name` (string): Field name (max 256 chars)
  - `value` (string): Field value (max 1024 chars)
  - `inline` (boolean, optional): Display inline

**Example Request:**
```json
{
  "method": "tools/call",
  "params": {
    "name": "send_message",
    "arguments": {
      "channel_id": "123456789012345678",
      "content": "Hello from MCP!",
      "embed": {
        "title": "Status Update",
        "description": "System is operational",
        "color": 65280,
        "fields": [
          {
            "name": "Uptime",
            "value": "99.9%",
            "inline": true
          }
        ]
      }
    }
  }
}
```

**Example Response:**
```json
{
  "content": [
    {
      "type": "text",
      "text": "{\"success\": true, \"message\": {\"id\": \"987654321098765432\", \"channel_id\": \"123456789012345678\", \"content\": \"Hello from MCP!\", \"timestamp\": \"2024-01-15T10:30:00.000Z\", \"url\": \"https://discord.com/channels/guild/channel/message\", \"author\": {\"id\": \"bot_id\", \"username\": \"MCPBot\", \"bot\": true}}}"
    }
  ]
}
```

### 2. get_messages

Retrieve message history from a Discord channel.

**Parameters:**
- `channel_id` (string, required): Discord channel ID
- `limit` (number, optional): Number of messages (1-100, default: 50)
- `before` (string, optional): Get messages before this message ID
- `after` (string, optional): Get messages after this message ID

**Example Request:**
```json
{
  "method": "tools/call",
  "params": {
    "name": "get_messages",
    "arguments": {
      "channel_id": "123456789012345678",
      "limit": 10,
      "before": "987654321098765432"
    }
  }
}
```

**Example Response:**
```json
{
  "content": [
    {
      "type": "text",
      "text": "{\"success\": true, \"channel\": {\"id\": \"123456789012345678\", \"name\": \"general\", \"type\": 0}, \"messages\": [{\"id\": \"message_id\", \"content\": \"Message content\", \"author\": {\"id\": \"user_id\", \"username\": \"Username\", \"discriminator\": \"0001\"}, \"timestamp\": \"2024-01-15T10:30:00.000Z\"}], \"pagination\": {\"requested_limit\": 10, \"actual_count\": 10, \"has_more\": true}}"
    }
  ]
}
```

### 3. get_channel_info

Get detailed information about a Discord channel.

**Parameters:**
- `channel_id` (string, required): Discord channel ID

**Example Request:**
```json
{
  "method": "tools/call",
  "params": {
    "name": "get_channel_info",
    "arguments": {
      "channel_id": "123456789012345678"
    }
  }
}
```

**Example Response:**
```json
{
  "content": [
    {
      "type": "text",
      "text": "{\"success\": true, \"channel\": {\"id\": \"123456789012345678\", \"name\": \"general\", \"type\": 0, \"type_name\": \"Text Channel\", \"guild\": {\"id\": \"guild_id\", \"name\": \"Server Name\", \"member_count\": 150}, \"permissions\": {\"can_send_messages\": true, \"can_read_history\": true, \"can_manage_messages\": true}, \"recent_activity\": {\"message_count_last_10\": 5, \"last_message_timestamp\": \"2024-01-15T10:30:00.000Z\"}}}"
    }
  ]
}
```

### 4. search_messages

Search for messages across channels with advanced filters.

**Parameters:**
- `query` (string, required): Search query (1-500 characters)
- `channel_id` (string, optional): Limit search to specific channel
- `author_id` (string, optional): Filter by message author
- `date_from` (string, optional): Start date (ISO string)
- `date_to` (string, optional): End date (ISO string)
- `limit` (number, optional): Number of results (1-50, default: 25)

**Example Request:**
```json
{
  "method": "tools/call",
  "params": {
    "name": "search_messages",
    "arguments": {
      "query": "important announcement",
      "channel_id": "123456789012345678",
      "date_from": "2024-01-01T00:00:00.000Z",
      "limit": 10
    }
  }
}
```

**Example Response:**
```json
{
  "content": [
    {
      "type": "text",
      "text": "{\"success\": true, \"query\": \"important announcement\", \"results\": {\"messages\": [{\"id\": \"message_id\", \"content\": \"Important announcement: ...\", \"author\": {\"id\": \"user_id\", \"username\": \"Admin\"}, \"timestamp\": \"2024-01-15T10:30:00.000Z\"}], \"total_found\": 3, \"channels_searched\": [\"123456789012345678\"], \"search_duration_ms\": 150}}"
    }
  ]
}
```

### 5. moderate_content

Perform moderation actions on Discord content or users.

**Parameters:**
- `action` (string, required): Moderation action
  - `delete_message`: Delete a specific message
  - `timeout_user`: Timeout a user (1 min - 28 days)
  - `ban_user`: Ban a user from the server
  - `kick_user`: Kick a user from the server
- `target_id` (string, required): Message ID or User ID
- `reason` (string, optional): Reason for moderation action (max 512 chars)
- `duration` (number, optional): Duration in seconds (for timeout_user only)

**Example Request (Delete Message):**
```json
{
  "method": "tools/call",
  "params": {
    "name": "moderate_content",
    "arguments": {
      "action": "delete_message",
      "target_id": "987654321098765432",
      "reason": "Spam content"
    }
  }
}
```

**Example Request (Timeout User):**
```json
{
  "method": "tools/call",
  "params": {
    "name": "moderate_content",
    "arguments": {
      "action": "timeout_user",
      "target_id": "123456789012345678",
      "reason": "Inappropriate behavior",
      "duration": 3600
    }
  }
}
```

**Example Response:**
```json
{
  "content": [
    {
      "type": "text",
      "text": "{\"success\": true, \"action\": \"delete_message\", \"target_id\": \"987654321098765432\", \"reason\": \"Spam content\", \"result\": {\"deleted_message\": {\"id\": \"987654321098765432\", \"content\": \"Deleted content\", \"author\": {\"id\": \"user_id\", \"username\": \"Username\"}}, \"deleted_at\": \"2024-01-15T10:30:00.000Z\"}}"
    }
  ]
}
```

## Error Handling

All tools return structured error responses:

```json
{
  "content": [
    {
      "type": "text",
      "text": "Error (400): Invalid channel ID format"
    }
  ],
  "isError": true
}
```

**Common Error Codes:**
- `400`: Validation Error (invalid input)
- `401`: Authentication Error (invalid API key)
- `403`: Authorization Error (insufficient permissions)
- `429`: Rate Limit Error (too many requests)
- `500`: Internal Server Error
- `502`: Discord API Error

## Permissions

Each API key has specific permissions. Required permissions by tool:

- `send_message`: `discord:send_message`
- `get_messages`: `discord:get_messages`
- `get_channel_info`: `discord:get_channel_info`
- `search_messages`: `discord:search_messages`
- `moderate_content`: `discord:moderate_content`

## Best Practices

1. **Rate Limiting**: Implement client-side rate limiting
2. **Error Handling**: Always check for error responses
3. **Input Validation**: Validate inputs before sending requests
4. **Security**: Never expose API keys in client-side code
5. **Logging**: Log important operations for audit trails
6. **Permissions**: Use principle of least privilege for API keys

## Support

- **Documentation**: Check `/docs` folder for additional guides
- **Issues**: Report bugs via GitHub issues
- **Inspector**: Use MCP Inspector for debugging (port 3001)
