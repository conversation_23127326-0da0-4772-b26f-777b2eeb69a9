import OpenAI from 'openai';
import { pipeline, env } from '@xenova/transformers';
import { ProcessedText, EmbeddingVector, EmbeddingModel, EmbeddingError } from '../types/index.js';
import { openaiConfig, modelConfig } from '../utils/config.js';

// Disable local model loading for transformers
env.allowLocalModels = false;

/**
 * Embedding Generation Service
 * Handles embedding generation using multiple models (OpenAI, Sentence Transformers)
 */
export class EmbeddingService {
  private openaiClient: OpenAI;
  private transformerPipelines: Map<string, any> = new Map();
  private modelCache: Map<string, EmbeddingModel> = new Map();

  constructor() {
    this.openaiClient = new OpenAI({
      apiKey: openaiConfig.apiKey,
    });
    
    this.initializeModels();
  }

  /**
   * Initialize available embedding models
   */
  private initializeModels(): void {
    const models: EmbeddingModel[] = [
      {
        name: 'text-embedding-ada-002',
        displayName: 'OpenAI Ada-002',
        provider: 'openai',
        dimensions: 1536,
        maxTokens: 8191,
        costPerToken: 0.0001,
        description: 'OpenAI\'s most capable embedding model with high accuracy',
        strengths: ['High accuracy', 'Large context window', 'Multilingual support'],
        limitations: ['Requires API key', 'Cost per usage', 'Rate limits'],
        recommendedFor: ['Production use', 'High accuracy requirements', 'Multilingual content'],
      },
      {
        name: 'all-MiniLM-L6-v2',
        displayName: 'Sentence-BERT MiniLM',
        provider: 'sentence-transformers',
        dimensions: 384,
        maxTokens: 512,
        description: 'Fast and efficient sentence transformer model',
        strengths: ['Fast inference', 'Good performance', 'No API costs'],
        limitations: ['Smaller context window', 'English-focused'],
        recommendedFor: ['Development', 'Fast processing', 'Cost-sensitive applications'],
      },
      {
        name: 'all-mpnet-base-v2',
        displayName: 'Sentence-BERT MPNet',
        provider: 'sentence-transformers',
        dimensions: 768,
        maxTokens: 512,
        description: 'High-quality sentence transformer with better accuracy',
        strengths: ['High accuracy', 'Good semantic understanding', 'No API costs'],
        limitations: ['Slower than MiniLM', 'Larger model size'],
        recommendedFor: ['High accuracy requirements', 'Semantic similarity tasks'],
      },
    ];

    models.forEach(model => {
      this.modelCache.set(model.name, model);
    });
  }

  /**
   * Generate embeddings for processed texts using specified model
   */
  async generateEmbeddings(
    processedTexts: ProcessedText[],
    modelName: string
  ): Promise<EmbeddingVector[]> {
    const model = this.modelCache.get(modelName);
    if (!model) {
      throw new EmbeddingError(`Unknown model: ${modelName}`);
    }

    const embeddings: EmbeddingVector[] = [];

    for (const text of processedTexts) {
      try {
        const startTime = Date.now();
        let vector: number[];

        switch (model.provider) {
          case 'openai':
            vector = await this.generateOpenAIEmbedding(text.processedContent, modelName);
            break;
          case 'sentence-transformers':
            vector = await this.generateTransformerEmbedding(text.processedContent, modelName);
            break;
          default:
            throw new EmbeddingError(`Unsupported model provider: ${model.provider}`);
        }

        const processingTime = Date.now() - startTime;

        embeddings.push({
          textId: text.id,
          model: modelName,
          vector,
          dimensions: vector.length,
          metadata: {
            modelVersion: model.displayName,
            processingTime,
            timestamp: new Date(),
          },
        });
      } catch (error) {
        throw new EmbeddingError(
          `Failed to generate embedding for text ${text.id} with model ${modelName}: ${error}`
        );
      }
    }

    return embeddings;
  }

  /**
   * Generate embeddings using OpenAI API
   */
  private async generateOpenAIEmbedding(text: string, model: string): Promise<number[]> {
    try {
      // Check text length against model limits
      const modelInfo = this.modelCache.get(model);
      if (modelInfo && text.length > modelInfo.maxTokens * 4) { // Rough token estimation
        throw new EmbeddingError(`Text too long for model ${model}. Max tokens: ${modelInfo.maxTokens}`);
      }

      const response = await this.openaiClient.embeddings.create({
        model: model,
        input: text,
        encoding_format: 'float',
      });

      if (!response.data || response.data.length === 0) {
        throw new EmbeddingError('No embedding data received from OpenAI');
      }

      return response.data[0].embedding;
    } catch (error) {
      if (error instanceof EmbeddingError) {
        throw error;
      }
      throw new EmbeddingError(`OpenAI API error: ${error}`);
    }
  }

  /**
   * Generate embeddings using Sentence Transformers
   */
  private async generateTransformerEmbedding(text: string, modelName: string): Promise<number[]> {
    try {
      // Get or create pipeline for this model
      let pipe = this.transformerPipelines.get(modelName);
      
      if (!pipe) {
        console.log(`Loading transformer model: ${modelName}`);
        pipe = await pipeline('feature-extraction', `sentence-transformers/${modelName}`);
        this.transformerPipelines.set(modelName, pipe);
      }

      // Generate embedding
      const output = await pipe(text, { pooling: 'mean', normalize: true });
      
      // Convert to regular array
      const embedding = Array.from(output.data);
      
      return embedding;
    } catch (error) {
      throw new EmbeddingError(`Transformer model error: ${error}`);
    }
  }

  /**
   * Generate embeddings using multiple models for comparison
   */
  async generateMultiModelEmbeddings(
    processedTexts: ProcessedText[],
    modelNames: string[]
  ): Promise<Record<string, EmbeddingVector[]>> {
    const results: Record<string, EmbeddingVector[]> = {};

    for (const modelName of modelNames) {
      try {
        console.log(`Generating embeddings with model: ${modelName}`);
        results[modelName] = await this.generateEmbeddings(processedTexts, modelName);
      } catch (error) {
        console.error(`Failed to generate embeddings with model ${modelName}:`, error);
        // Continue with other models even if one fails
        results[modelName] = [];
      }
    }

    return results;
  }

  /**
   * Get available models
   */
  getAvailableModels(): EmbeddingModel[] {
    return Array.from(this.modelCache.values());
  }

  /**
   * Get model information
   */
  getModelInfo(modelName: string): EmbeddingModel | undefined {
    return this.modelCache.get(modelName);
  }

  /**
   * Validate model availability
   */
  isModelAvailable(modelName: string): boolean {
    return this.modelCache.has(modelName);
  }

  /**
   * Get recommended models for plagiarism detection
   */
  getRecommendedModels(): string[] {
    return [
      'text-embedding-ada-002', // High accuracy
      'all-mpnet-base-v2',      // Good balance
      'all-MiniLM-L6-v2',       // Fast processing
    ];
  }

  /**
   * Estimate processing time for given texts and models
   */
  estimateProcessingTime(textCount: number, modelNames: string[]): number {
    let totalTime = 0;

    for (const modelName of modelNames) {
      const model = this.modelCache.get(modelName);
      if (model) {
        switch (model.provider) {
          case 'openai':
            totalTime += textCount * 2000; // ~2 seconds per text for API calls
            break;
          case 'sentence-transformers':
            totalTime += textCount * 500; // ~0.5 seconds per text for local models
            break;
        }
      }
    }

    return totalTime;
  }

  /**
   * Clean up resources
   */
  async cleanup(): Promise<void> {
    // Clear transformer pipelines to free memory
    this.transformerPipelines.clear();
    console.log('Embedding service cleaned up');
  }
}
