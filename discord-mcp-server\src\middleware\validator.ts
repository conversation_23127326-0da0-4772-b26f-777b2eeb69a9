import { z } from 'zod';
import { loggers } from '../utils/logger.js';
import { ValidationError } from '../types/index.js';

/**
 * Input validation middleware for MCP requests
 * Provides comprehensive validation and sanitization
 */
export class InputValidator {
  
  /**
   * Validate and sanitize string input
   */
  static validateString(
    value: any, 
    fieldName: string, 
    options: {
      required?: boolean;
      minLength?: number;
      maxLength?: number;
      pattern?: RegExp;
      allowEmpty?: boolean;
    } = {}
  ): string {
    const {
      required = true,
      minLength = 0,
      maxLength = 10000,
      pattern,
      allowEmpty = false
    } = options;

    // Check if value exists
    if (value === undefined || value === null) {
      if (required) {
        throw new ValidationError(`${fieldName} is required`);
      }
      return '';
    }

    // Convert to string
    const stringValue = String(value).trim();

    // Check empty string
    if (!allowEmpty && stringValue.length === 0 && required) {
      throw new ValidationError(`${fieldName} cannot be empty`);
    }

    // Check length
    if (stringValue.length < minLength) {
      throw new ValidationError(`${fieldName} must be at least ${minLength} characters long`);
    }

    if (stringValue.length > maxLength) {
      throw new ValidationError(`${fieldName} cannot exceed ${maxLength} characters`);
    }

    // Check pattern
    if (pattern && !pattern.test(stringValue)) {
      throw new ValidationError(`${fieldName} format is invalid`);
    }

    // Sanitize for potential XSS
    const sanitized = this.sanitizeString(stringValue);

    loggers.debug('String validation passed', {
      fieldName,
      originalLength: stringValue.length,
      sanitizedLength: sanitized.length
    });

    return sanitized;
  }

  /**
   * Validate Discord ID format
   */
  static validateDiscordId(value: any, fieldName: string): string {
    const discordIdPattern = /^\d{17,19}$/; // Discord IDs are 17-19 digit numbers
    
    return this.validateString(value, fieldName, {
      required: true,
      minLength: 17,
      maxLength: 19,
      pattern: discordIdPattern
    });
  }

  /**
   * Validate message content
   */
  static validateMessageContent(content: any): string {
    return this.validateString(content, 'message content', {
      required: true,
      minLength: 1,
      maxLength: 2000, // Discord's message limit
      allowEmpty: false
    });
  }

  /**
   * Validate search query
   */
  static validateSearchQuery(query: any): string {
    return this.validateString(query, 'search query', {
      required: true,
      minLength: 1,
      maxLength: 500,
      allowEmpty: false
    });
  }

  /**
   * Validate number input
   */
  static validateNumber(
    value: any,
    fieldName: string,
    options: {
      required?: boolean;
      min?: number;
      max?: number;
      integer?: boolean;
    } = {}
  ): number {
    const {
      required = true,
      min = Number.MIN_SAFE_INTEGER,
      max = Number.MAX_SAFE_INTEGER,
      integer = false
    } = options;

    // Check if value exists
    if (value === undefined || value === null) {
      if (required) {
        throw new ValidationError(`${fieldName} is required`);
      }
      return 0;
    }

    // Convert to number
    const numValue = Number(value);

    // Check if valid number
    if (isNaN(numValue) || !isFinite(numValue)) {
      throw new ValidationError(`${fieldName} must be a valid number`);
    }

    // Check integer requirement
    if (integer && !Number.isInteger(numValue)) {
      throw new ValidationError(`${fieldName} must be an integer`);
    }

    // Check range
    if (numValue < min) {
      throw new ValidationError(`${fieldName} must be at least ${min}`);
    }

    if (numValue > max) {
      throw new ValidationError(`${fieldName} cannot exceed ${max}`);
    }

    loggers.debug('Number validation passed', {
      fieldName,
      value: numValue,
      min,
      max
    });

    return numValue;
  }

  /**
   * Validate date string
   */
  static validateDate(value: any, fieldName: string, required: boolean = false): Date | null {
    if (value === undefined || value === null) {
      if (required) {
        throw new ValidationError(`${fieldName} is required`);
      }
      return null;
    }

    const dateValue = new Date(value);

    if (isNaN(dateValue.getTime())) {
      throw new ValidationError(`${fieldName} must be a valid ISO date string`);
    }

    // Check reasonable date range (not too far in past or future)
    const now = new Date();
    const minDate = new Date('2015-01-01'); // Discord was founded in 2015
    const maxDate = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000); // 1 year in future

    if (dateValue < minDate) {
      throw new ValidationError(`${fieldName} cannot be before ${minDate.toISOString()}`);
    }

    if (dateValue > maxDate) {
      throw new ValidationError(`${fieldName} cannot be after ${maxDate.toISOString()}`);
    }

    loggers.debug('Date validation passed', {
      fieldName,
      value: dateValue.toISOString()
    });

    return dateValue;
  }

  /**
   * Validate embed object
   */
  static validateEmbed(embed: any): any {
    if (!embed || typeof embed !== 'object') {
      return null;
    }

    const validatedEmbed: any = {};

    // Validate title
    if (embed.title !== undefined) {
      validatedEmbed.title = this.validateString(embed.title, 'embed title', {
        required: false,
        maxLength: 256
      });
    }

    // Validate description
    if (embed.description !== undefined) {
      validatedEmbed.description = this.validateString(embed.description, 'embed description', {
        required: false,
        maxLength: 4096
      });
    }

    // Validate color
    if (embed.color !== undefined) {
      validatedEmbed.color = this.validateNumber(embed.color, 'embed color', {
        required: false,
        min: 0,
        max: 16777215, // Max color value (0xFFFFFF)
        integer: true
      });
    }

    // Validate fields
    if (embed.fields !== undefined) {
      if (!Array.isArray(embed.fields)) {
        throw new ValidationError('Embed fields must be an array');
      }

      if (embed.fields.length > 25) {
        throw new ValidationError('Embed cannot have more than 25 fields');
      }

      validatedEmbed.fields = embed.fields.map((field: any, index: number) => {
        if (!field || typeof field !== 'object') {
          throw new ValidationError(`Embed field ${index} must be an object`);
        }

        return {
          name: this.validateString(field.name, `embed field ${index} name`, {
            required: true,
            maxLength: 256
          }),
          value: this.validateString(field.value, `embed field ${index} value`, {
            required: true,
            maxLength: 1024
          }),
          inline: field.inline === true
        };
      });
    }

    // Calculate total embed length
    const totalLength = 
      (validatedEmbed.title?.length || 0) +
      (validatedEmbed.description?.length || 0) +
      (validatedEmbed.fields?.reduce((sum: number, field: any) => 
        sum + field.name.length + field.value.length, 0) || 0);

    if (totalLength > 6000) {
      throw new ValidationError('Total embed content cannot exceed 6000 characters');
    }

    loggers.debug('Embed validation passed', {
      totalLength,
      fieldsCount: validatedEmbed.fields?.length || 0
    });

    return validatedEmbed;
  }

  /**
   * Sanitize string to prevent XSS and injection attacks
   */
  private static sanitizeString(input: string): string {
    // Remove null bytes
    let sanitized = input.replace(/\0/g, '');

    // Remove or escape potentially dangerous characters
    sanitized = sanitized
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // Remove control characters
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();

    // Additional sanitization for specific contexts could be added here
    // For example, HTML entity encoding, SQL injection prevention, etc.

    return sanitized;
  }

  /**
   * Validate moderation action
   */
  static validateModerationAction(action: any): string {
    const validActions = ['delete_message', 'timeout_user', 'ban_user', 'kick_user'];
    
    if (!validActions.includes(action)) {
      throw new ValidationError(`Invalid moderation action. Must be one of: ${validActions.join(', ')}`);
    }

    return action;
  }

  /**
   * Validate timeout duration
   */
  static validateTimeoutDuration(duration: any): number {
    return this.validateNumber(duration, 'timeout duration', {
      required: true,
      min: 60, // 1 minute minimum
      max: 2419200, // 28 days maximum (Discord limit)
      integer: true
    });
  }

  /**
   * Validate pagination parameters
   */
  static validatePagination(limit?: any, before?: any, after?: any): {
    limit: number;
    before?: string;
    after?: string;
  } {
    const result: any = {};

    // Validate limit
    result.limit = this.validateNumber(limit || 50, 'limit', {
      required: false,
      min: 1,
      max: 100,
      integer: true
    });

    // Validate before (Discord message ID)
    if (before !== undefined) {
      result.before = this.validateDiscordId(before, 'before message ID');
    }

    // Validate after (Discord message ID)
    if (after !== undefined) {
      result.after = this.validateDiscordId(after, 'after message ID');
    }

    return result;
  }
}

export { InputValidator };
