import winston from 'winston';
import { config } from '../config/index.js';

// Custom log format
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}]: ${message}`;
    
    if (Object.keys(meta).length > 0) {
      log += ` ${JSON.stringify(meta)}`;
    }
    
    if (stack) {
      log += `\n${stack}`;
    }
    
    return log;
  })
);

// Create logger instance
export const logger = winston.createLogger({
  level: config.logging.level,
  format: logFormat,
  transports: [
    // Console transport
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    
    // File transport
    new winston.transports.File({
      filename: config.logging.file,
      maxsize: parseSize(config.logging.maxSize),
      maxFiles: config.logging.maxFiles,
      tailable: true
    }),
    
    // Error file transport
    new winston.transports.File({
      filename: config.logging.file.replace('.log', '.error.log'),
      level: 'error',
      maxsize: parseSize(config.logging.maxSize),
      maxFiles: config.logging.maxFiles,
      tailable: true
    })
  ],
  
  // Handle uncaught exceptions
  exceptionHandlers: [
    new winston.transports.File({
      filename: config.logging.file.replace('.log', '.exceptions.log')
    })
  ],
  
  // Handle unhandled promise rejections
  rejectionHandlers: [
    new winston.transports.File({
      filename: config.logging.file.replace('.log', '.rejections.log')
    })
  ]
});

// Helper function to parse size strings like "10m", "1g"
function parseSize(sizeStr: string): number {
  const match = sizeStr.match(/^(\d+)([kmg]?)$/i);
  if (!match) return 10 * 1024 * 1024; // Default 10MB
  
  const size = parseInt(match[1]);
  const unit = match[2]?.toLowerCase() || '';
  
  switch (unit) {
    case 'k': return size * 1024;
    case 'm': return size * 1024 * 1024;
    case 'g': return size * 1024 * 1024 * 1024;
    default: return size;
  }
}

// Audit logger for security events
export const auditLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({
      filename: config.logging.file.replace('.log', '.audit.log'),
      maxsize: parseSize(config.logging.maxSize),
      maxFiles: config.logging.maxFiles,
      tailable: true
    })
  ]
});

// Performance logger for monitoring
export const performanceLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({
      filename: config.logging.file.replace('.log', '.performance.log'),
      maxsize: parseSize(config.logging.maxSize),
      maxFiles: config.logging.maxFiles,
      tailable: true
    })
  ]
});

// Structured logging helpers
export const loggers = {
  // General application logging
  info: (message: string, meta?: any) => logger.info(message, meta),
  warn: (message: string, meta?: any) => logger.warn(message, meta),
  error: (message: string, error?: Error, meta?: any) => {
    logger.error(message, { error: error?.message, stack: error?.stack, ...meta });
  },
  debug: (message: string, meta?: any) => logger.debug(message, meta),
  
  // Audit logging for security events
  audit: (action: string, details: any) => {
    auditLogger.info('AUDIT_EVENT', {
      action,
      timestamp: new Date().toISOString(),
      ...details
    });
  },
  
  // Performance logging
  performance: (operation: string, duration: number, meta?: any) => {
    performanceLogger.info('PERFORMANCE_METRIC', {
      operation,
      duration,
      timestamp: new Date().toISOString(),
      ...meta
    });
  },
  
  // MCP-specific logging
  mcpRequest: (toolName: string, args: any, tenantId?: string) => {
    logger.info('MCP_TOOL_REQUEST', {
      tool: toolName,
      args,
      tenantId,
      timestamp: new Date().toISOString()
    });
  },
  
  mcpResponse: (toolName: string, success: boolean, duration: number, error?: string) => {
    logger.info('MCP_TOOL_RESPONSE', {
      tool: toolName,
      success,
      duration,
      error,
      timestamp: new Date().toISOString()
    });
  },
  
  // Discord API logging
  discordRequest: (endpoint: string, method: string, tenantId?: string) => {
    logger.debug('DISCORD_API_REQUEST', {
      endpoint,
      method,
      tenantId,
      timestamp: new Date().toISOString()
    });
  },
  
  discordResponse: (endpoint: string, status: number, duration: number, error?: string) => {
    logger.debug('DISCORD_API_RESPONSE', {
      endpoint,
      status,
      duration,
      error,
      timestamp: new Date().toISOString()
    });
  }
};

// Export default logger
export default logger;
