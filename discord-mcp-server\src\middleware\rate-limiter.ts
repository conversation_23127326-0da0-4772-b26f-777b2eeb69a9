import { loggers } from '../utils/logger.js';
import { config } from '../config/index.js';
import { RateLimitError } from '../types/index.js';

interface RateLimitEntry {
  count: number;
  windowStart: number;
  lastRequest: number;
}

/**
 * Rate Limiter for MCP requests
 * Implements sliding window rate limiting per tenant and tool
 */
export class RateLimiter {
  private limits: Map<string, RateLimitEntry> = new Map();
  private readonly windowMs: number;
  private readonly maxRequests: number;
  private cleanupInterval: NodeJS.Timeout;

  constructor() {
    this.windowMs = config.rateLimit.windowMs;
    this.maxRequests = config.rateLimit.maxRequests;
    
    // Cleanup expired entries every minute
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 60000);

    loggers.info('Rate limiter initialized', {
      windowMs: this.windowMs,
      maxRequests: this.maxRequests
    });
  }

  /**
   * Check if request is within rate limits
   */
  async checkLimit(tenantId: string, toolName: string, customLimit?: number): Promise<void> {
    const key = `${tenantId}:${toolName}`;
    const now = Date.now();
    const limit = customLimit || this.maxRequests;
    
    let entry = this.limits.get(key);
    
    if (!entry) {
      // First request for this key
      entry = {
        count: 1,
        windowStart: now,
        lastRequest: now
      };
      this.limits.set(key, entry);
      
      loggers.debug('Rate limit entry created', { 
        tenantId, 
        toolName, 
        count: 1, 
        limit 
      });
      
      return;
    }

    // Check if we're in a new window
    if (now - entry.windowStart >= this.windowMs) {
      // Reset for new window
      entry.count = 1;
      entry.windowStart = now;
      entry.lastRequest = now;
      
      loggers.debug('Rate limit window reset', { 
        tenantId, 
        toolName, 
        count: 1, 
        limit 
      });
      
      return;
    }

    // Check if limit exceeded
    if (entry.count >= limit) {
      const resetTime = entry.windowStart + this.windowMs;
      const waitTime = resetTime - now;
      
      loggers.warn('Rate limit exceeded', {
        tenantId,
        toolName,
        count: entry.count,
        limit,
        waitTime
      });

      throw new RateLimitError(
        `Rate limit exceeded. Try again in ${Math.ceil(waitTime / 1000)} seconds.`
      );
    }

    // Increment counter
    entry.count++;
    entry.lastRequest = now;
    
    loggers.debug('Rate limit check passed', { 
      tenantId, 
      toolName, 
      count: entry.count, 
      limit 
    });
  }

  /**
   * Get current rate limit status for a tenant/tool
   */
  getStatus(tenantId: string, toolName: string): {
    count: number;
    limit: number;
    remaining: number;
    resetTime: number;
    windowMs: number;
  } {
    const key = `${tenantId}:${toolName}`;
    const entry = this.limits.get(key);
    const now = Date.now();
    
    if (!entry) {
      return {
        count: 0,
        limit: this.maxRequests,
        remaining: this.maxRequests,
        resetTime: now + this.windowMs,
        windowMs: this.windowMs
      };
    }

    // Check if window has expired
    if (now - entry.windowStart >= this.windowMs) {
      return {
        count: 0,
        limit: this.maxRequests,
        remaining: this.maxRequests,
        resetTime: now + this.windowMs,
        windowMs: this.windowMs
      };
    }

    return {
      count: entry.count,
      limit: this.maxRequests,
      remaining: Math.max(0, this.maxRequests - entry.count),
      resetTime: entry.windowStart + this.windowMs,
      windowMs: this.windowMs
    };
  }

  /**
   * Get rate limit statistics for all tenants
   */
  getStatistics(): Record<string, any> {
    const stats: Record<string, any> = {};
    const now = Date.now();
    
    for (const [key, entry] of this.limits.entries()) {
      const [tenantId, toolName] = key.split(':');
      
      if (!stats[tenantId]) {
        stats[tenantId] = {};
      }
      
      // Only include active windows
      if (now - entry.windowStart < this.windowMs) {
        stats[tenantId][toolName] = {
          count: entry.count,
          remaining: Math.max(0, this.maxRequests - entry.count),
          windowStart: entry.windowStart,
          lastRequest: entry.lastRequest
        };
      }
    }
    
    return stats;
  }

  /**
   * Reset rate limits for a specific tenant
   */
  resetTenant(tenantId: string): void {
    const keysToDelete: string[] = [];
    
    for (const key of this.limits.keys()) {
      if (key.startsWith(`${tenantId}:`)) {
        keysToDelete.push(key);
      }
    }
    
    for (const key of keysToDelete) {
      this.limits.delete(key);
    }
    
    loggers.info('Rate limits reset for tenant', { tenantId });
  }

  /**
   * Reset rate limits for a specific tool across all tenants
   */
  resetTool(toolName: string): void {
    const keysToDelete: string[] = [];
    
    for (const key of this.limits.keys()) {
      if (key.endsWith(`:${toolName}`)) {
        keysToDelete.push(key);
      }
    }
    
    for (const key of keysToDelete) {
      this.limits.delete(key);
    }
    
    loggers.info('Rate limits reset for tool', { toolName });
  }

  /**
   * Set custom rate limit for a specific tenant/tool combination
   */
  setCustomLimit(tenantId: string, toolName: string, limit: number, windowMs?: number): void {
    // This would typically be stored in database for persistence
    // For now, we'll just log it
    loggers.info('Custom rate limit set', {
      tenantId,
      toolName,
      limit,
      windowMs: windowMs || this.windowMs
    });
  }

  /**
   * Cleanup expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];
    
    for (const [key, entry] of this.limits.entries()) {
      // Remove entries older than 2 windows
      if (now - entry.lastRequest > this.windowMs * 2) {
        keysToDelete.push(key);
      }
    }
    
    for (const key of keysToDelete) {
      this.limits.delete(key);
    }
    
    if (keysToDelete.length > 0) {
      loggers.debug('Rate limiter cleanup completed', { 
        removedEntries: keysToDelete.length 
      });
    }
  }

  /**
   * Get total number of active rate limit entries
   */
  getActiveEntries(): number {
    return this.limits.size;
  }

  /**
   * Destroy the rate limiter and cleanup resources
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    
    this.limits.clear();
    loggers.info('Rate limiter destroyed');
  }
}

// Export singleton instance
export const rateLimiter = new RateLimiter();
