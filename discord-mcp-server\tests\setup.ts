import { beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals';
import { config } from '../src/config/index.js';
import { db } from '../src/database/models.js';
import { loggers } from '../src/utils/logger.js';

// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.DISCORD_BOT_TOKEN = 'test.token.here';
process.env.DISCORD_APPLICATION_ID = '123456789012345678';
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-purposes';
process.env.API_KEYS = 'test-api-key:test-tenant';
process.env.ENCRYPTION_KEY = 'test-encryption-key-32-characters';
process.env.DATABASE_URL = 'sqlite::memory:';
process.env.LOG_LEVEL = 'error'; // Reduce log noise during tests

// Global test setup
beforeAll(async () => {
  // Initialize test database
  await db.getDb();
  
  // Suppress console output during tests
  jest.spyOn(console, 'log').mockImplementation(() => {});
  jest.spyOn(console, 'warn').mockImplementation(() => {});
  jest.spyOn(console, 'error').mockImplementation(() => {});
  
  loggers.info('Test environment initialized');
});

// Global test cleanup
afterAll(async () => {
  // Close database connection
  await db.close();
  
  // Restore console methods
  jest.restoreAllMocks();
  
  loggers.info('Test environment cleaned up');
});

// Test isolation
beforeEach(async () => {
  // Clear any test data between tests if needed
});

afterEach(async () => {
  // Clean up after each test
  jest.clearAllMocks();
});

// Test utilities
export const testUtils = {
  /**
   * Create a test tenant
   */
  async createTestTenant(id: string = 'test-tenant') {
    return await db.createTenant({
      id,
      name: `Test Tenant ${id}`,
      discordBotToken: 'test.bot.token',
      permissions: [
        {
          resource: 'discord',
          actions: ['send_message', 'get_messages', 'get_channel_info', 'search_messages', 'moderate_content']
        }
      ],
      quotas: {
        maxRequestsPerMinute: 60,
        maxRequestsPerHour: 1000,
        maxMessageHistory: 100,
        maxSearchResults: 50
      }
    });
  },

  /**
   * Create a test API key
   */
  async createTestApiKey(tenantId: string = 'test-tenant', keyValue: string = 'test-api-key') {
    return await db.createApiKey({
      id: 'test-key-id',
      key: keyValue,
      tenantId,
      name: 'Test API Key',
      permissions: [
        {
          resource: 'discord',
          actions: ['send_message', 'get_messages', 'get_channel_info', 'search_messages', 'moderate_content']
        }
      ]
    });
  },

  /**
   * Create a mock Discord client
   */
  createMockDiscordClient() {
    return {
      user: {
        id: '123456789012345678',
        username: 'TestBot',
        discriminator: '0001',
        avatarURL: () => 'https://example.com/avatar.png',
        verified: true
      },
      guilds: {
        cache: new Map([
          ['guild1', {
            id: 'guild1',
            name: 'Test Guild',
            memberCount: 100,
            channels: {
              cache: new Map([
                ['channel1', {
                  id: 'channel1',
                  name: 'general',
                  type: 0,
                  send: jest.fn().mockResolvedValue({
                    id: 'message1',
                    content: 'Test message',
                    author: { id: '123456789012345678', username: 'TestBot' },
                    channelId: 'channel1',
                    createdAt: new Date(),
                    url: 'https://discord.com/channels/guild1/channel1/message1'
                  }),
                  messages: {
                    fetch: jest.fn().mockResolvedValue(new Map())
                  },
                  permissionsFor: jest.fn().mockReturnValue({
                    has: jest.fn().mockReturnValue(true),
                    toArray: jest.fn().mockReturnValue(['SendMessages', 'ReadMessageHistory'])
                  })
                }])
              ]
            },
            members: {
              me: {
                id: '123456789012345678',
                permissions: {
                  has: jest.fn().mockReturnValue(true),
                  toArray: jest.fn().mockReturnValue(['SendMessages', 'ManageMessages'])
                }
              },
              fetch: jest.fn().mockResolvedValue({
                id: 'user1',
                user: { username: 'TestUser' },
                ban: jest.fn().mockResolvedValue(undefined),
                kick: jest.fn().mockResolvedValue(undefined),
                timeout: jest.fn().mockResolvedValue(undefined)
              })
            }
          }])
        ])
      ],
      channels: {
        fetch: jest.fn().mockImplementation((id) => {
          if (id === 'channel1') {
            return Promise.resolve({
              id: 'channel1',
              name: 'general',
              type: 0,
              send: jest.fn().mockResolvedValue({
                id: 'message1',
                content: 'Test message',
                author: { id: '123456789012345678', username: 'TestBot' },
                channelId: 'channel1',
                createdAt: new Date(),
                url: 'https://discord.com/channels/guild1/channel1/message1'
              }),
              messages: {
                fetch: jest.fn().mockResolvedValue(new Map([
                  ['message1', {
                    id: 'message1',
                    content: 'Test message content',
                    author: {
                      id: 'user1',
                      username: 'TestUser',
                      discriminator: '0001'
                    },
                    channelId: 'channel1',
                    guildId: 'guild1',
                    createdAt: new Date(),
                    editedAt: null,
                    embeds: []
                  }]
                ]))
              },
              permissionsFor: jest.fn().mockReturnValue({
                has: jest.fn().mockReturnValue(true)
              })
            });
          }
          return Promise.reject(new Error('Channel not found'));
        })
      },
      login: jest.fn().mockResolvedValue('token'),
      destroy: jest.fn().mockResolvedValue(undefined),
      on: jest.fn(),
      once: jest.fn(),
      emit: jest.fn()
    };
  },

  /**
   * Create a mock MCP request
   */
  createMockMCPRequest(toolName: string, args: any, apiKey: string = 'test-api-key') {
    return {
      method: 'tools/call',
      params: {
        name: toolName,
        arguments: args
      },
      meta: {
        apiKey
      }
    };
  },

  /**
   * Wait for a specified amount of time
   */
  async wait(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
  },

  /**
   * Generate test data
   */
  generateTestData: {
    discordId: () => Math.floor(Math.random() * 9000000000000000000 + 1000000000000000000).toString(),
    messageContent: () => `Test message ${Math.random().toString(36).substring(7)}`,
    apiKey: () => `test_${Math.random().toString(36).substring(7)}`,
    tenantId: () => `tenant_${Math.random().toString(36).substring(7)}`
  }
};

// Export test configuration
export const testConfig = {
  timeout: 10000,
  retries: 3,
  verbose: false
};
