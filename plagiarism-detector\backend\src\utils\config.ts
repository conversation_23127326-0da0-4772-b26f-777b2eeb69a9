import { config } from 'dotenv';
import { z } from 'zod';

// Load environment variables
config();

// Environment validation schema
const envSchema = z.object({
  // Server Configuration
  PORT: z.string().default('3001').transform(Number),
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  API_BASE_URL: z.string().url().default('http://localhost:3001'),

  // CORS Configuration
  FRONTEND_URL: z.string().url().default('http://localhost:5173'),
  ALLOWED_ORIGINS: z.string().default('http://localhost:5173,http://localhost:3000'),

  // OpenAI Configuration
  OPENAI_API_KEY: z.string().min(1, 'OpenAI API key is required'),
  OPENAI_MODEL: z.string().default('text-embedding-ada-002'),
  OPENAI_MAX_TOKENS: z.string().default('8191').transform(Number),

  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: z.string().default('900000').transform(Number),
  RATE_LIMIT_MAX_REQUESTS: z.string().default('100').transform(Number),
  RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS: z.string().default('false').transform(val => val === 'true'),

  // Similarity Detection
  DEFAULT_SIMILARITY_THRESHOLD: z.string().default('0.8').transform(Number),
  MAX_TEXT_LENGTH: z.string().default('10000').transform(Number),
  MAX_TEXTS_PER_REQUEST: z.string().default('10').transform(Number),

  // Logging Configuration
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  LOG_FORMAT: z.enum(['json', 'simple']).default('json'),
  LOG_FILE_PATH: z.string().default('logs/app.log'),
  LOG_MAX_SIZE: z.string().default('10m'),
  LOG_MAX_FILES: z.string().default('5').transform(Number),

  // Security
  API_SECRET_KEY: z.string().min(32, 'API secret key must be at least 32 characters'),
  ENABLE_REQUEST_LOGGING: z.string().default('true').transform(val => val === 'true'),
  ENABLE_SECURITY_HEADERS: z.string().default('true').transform(val => val === 'true'),

  // Performance
  EMBEDDING_CACHE_TTL: z.string().default('3600').transform(Number),
  MAX_CONCURRENT_REQUESTS: z.string().default('50').transform(Number),
  REQUEST_TIMEOUT: z.string().default('30000').transform(Number),

  // Model Configuration
  SENTENCE_TRANSFORMER_MODEL: z.string().default('all-MiniLM-L6-v2'),
  ALTERNATIVE_MODEL: z.string().default('all-mpnet-base-v2'),
  ENABLE_MODEL_COMPARISON: z.string().default('true').transform(val => val === 'true'),

  // Development
  DEBUG_MODE: z.string().default('false').transform(val => val === 'true'),
  ENABLE_CORS: z.string().default('true').transform(val => val === 'true'),
  TRUST_PROXY: z.string().default('false').transform(val => val === 'true'),
});

// Validate and parse environment variables
const parseEnv = () => {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
      throw new Error(`Environment validation failed:\n${missingVars.join('\n')}`);
    }
    throw error;
  }
};

export const appConfig = parseEnv();

// Derived configurations
export const corsConfig = {
  origin: appConfig.ALLOWED_ORIGINS.split(',').map(origin => origin.trim()),
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
};

export const rateLimitConfig = {
  windowMs: appConfig.RATE_LIMIT_WINDOW_MS,
  max: appConfig.RATE_LIMIT_MAX_REQUESTS,
  skipSuccessfulRequests: appConfig.RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS,
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: Math.ceil(appConfig.RATE_LIMIT_WINDOW_MS / 1000),
  },
};

export const openaiConfig = {
  apiKey: appConfig.OPENAI_API_KEY,
  model: appConfig.OPENAI_MODEL,
  maxTokens: appConfig.OPENAI_MAX_TOKENS,
};

export const similarityConfig = {
  defaultThreshold: appConfig.DEFAULT_SIMILARITY_THRESHOLD,
  maxTextLength: appConfig.MAX_TEXT_LENGTH,
  maxTextsPerRequest: appConfig.MAX_TEXTS_PER_REQUEST,
};

export const modelConfig = {
  sentenceTransformerModel: appConfig.SENTENCE_TRANSFORMER_MODEL,
  alternativeModel: appConfig.ALTERNATIVE_MODEL,
  enableModelComparison: appConfig.ENABLE_MODEL_COMPARISON,
};

// Environment helpers
export const isDevelopment = appConfig.NODE_ENV === 'development';
export const isProduction = appConfig.NODE_ENV === 'production';
export const isTest = appConfig.NODE_ENV === 'test';

// Validation helper
export const validateConfig = () => {
  console.log('✅ Environment configuration validated successfully');
  console.log(`🚀 Server will run on port ${appConfig.PORT}`);
  console.log(`🌍 Environment: ${appConfig.NODE_ENV}`);
  console.log(`🔑 OpenAI model: ${appConfig.OPENAI_MODEL}`);
  console.log(`📊 Model comparison: ${appConfig.ENABLE_MODEL_COMPARISON ? 'enabled' : 'disabled'}`);
};
