import { Client, EmbedBuilder, TextChannel, DMChannel, NewsChannel, ThreadChannel } from 'discord.js';
import { z } from 'zod';
import { loggers } from '../utils/logger.js';
import { DiscordAPIError, ValidationError } from '../types/index.js';

// Input schema for send_message tool
const SendMessageInput = z.object({
  channel_id: z.string().min(1, "Channel ID is required"),
  content: z.string().min(1, "Message content is required").max(2000, "Message too long"),
  embed: z.object({
    title: z.string().optional(),
    description: z.string().optional(),
    color: z.number().optional(),
    fields: z.array(z.object({
      name: z.string(),
      value: z.string(),
      inline: z.boolean().optional()
    })).optional()
  }).optional()
});

type SendMessageArgs = z.infer<typeof SendMessageInput>;

/**
 * Send a message to a Discord channel
 */
export async function sendMessage(client: Client, args: SendMessageArgs) {
  const startTime = Date.now();
  
  try {
    loggers.info('Sending Discord message', {
      channelId: args.channel_id,
      contentLength: args.content.length,
      hasEmbed: !!args.embed
    });

    // Get the channel
    const channel = await client.channels.fetch(args.channel_id);
    
    if (!channel) {
      throw new ValidationError(`Channel not found: ${args.channel_id}`);
    }

    // Check if channel is a text-based channel
    if (!isTextBasedChannel(channel)) {
      throw new ValidationError(`Channel ${args.channel_id} is not a text-based channel`);
    }

    // Prepare message options
    const messageOptions: any = {
      content: args.content
    };

    // Add embed if provided
    if (args.embed) {
      const embed = new EmbedBuilder();
      
      if (args.embed.title) {
        embed.setTitle(args.embed.title);
      }
      
      if (args.embed.description) {
        embed.setDescription(args.embed.description);
      }
      
      if (args.embed.color) {
        embed.setColor(args.embed.color);
      }
      
      if (args.embed.fields && args.embed.fields.length > 0) {
        embed.addFields(args.embed.fields);
      }
      
      // Add timestamp
      embed.setTimestamp();
      
      messageOptions.embeds = [embed];
    }

    // Send the message
    const sentMessage = await channel.send(messageOptions);
    
    const duration = Date.now() - startTime;
    
    loggers.info('Discord message sent successfully', {
      channelId: args.channel_id,
      messageId: sentMessage.id,
      duration
    });

    // Return success response
    return {
      success: true,
      message: {
        id: sentMessage.id,
        channel_id: sentMessage.channelId,
        content: sentMessage.content,
        timestamp: sentMessage.createdAt.toISOString(),
        url: sentMessage.url,
        author: {
          id: sentMessage.author.id,
          username: sentMessage.author.username,
          bot: sentMessage.author.bot
        }
      },
      embed: args.embed ? {
        title: args.embed.title,
        description: args.embed.description,
        color: args.embed.color,
        fields: args.embed.fields
      } : undefined
    };

  } catch (error) {
    const duration = Date.now() - startTime;
    
    loggers.error('Failed to send Discord message', error as Error, {
      channelId: args.channel_id,
      duration
    });

    // Handle specific Discord API errors
    if (error instanceof Error) {
      if (error.message.includes('Missing Permissions')) {
        throw new DiscordAPIError('Bot lacks permission to send messages in this channel');
      }
      
      if (error.message.includes('Unknown Channel')) {
        throw new ValidationError(`Channel not found or bot cannot access: ${args.channel_id}`);
      }
      
      if (error.message.includes('Cannot send messages to this user')) {
        throw new DiscordAPIError('Cannot send DM to this user');
      }
    }

    // Re-throw validation errors as-is
    if (error instanceof ValidationError) {
      throw error;
    }

    // Wrap other errors
    throw new DiscordAPIError(`Failed to send message: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Type guard to check if channel supports sending messages
 */
function isTextBasedChannel(channel: any): channel is TextChannel | DMChannel | NewsChannel | ThreadChannel {
  return channel && typeof channel.send === 'function';
}

/**
 * Validate embed data
 */
function validateEmbed(embed: any): void {
  if (embed.title && embed.title.length > 256) {
    throw new ValidationError('Embed title cannot exceed 256 characters');
  }
  
  if (embed.description && embed.description.length > 4096) {
    throw new ValidationError('Embed description cannot exceed 4096 characters');
  }
  
  if (embed.fields) {
    if (embed.fields.length > 25) {
      throw new ValidationError('Embed cannot have more than 25 fields');
    }
    
    for (const field of embed.fields) {
      if (field.name && field.name.length > 256) {
        throw new ValidationError('Embed field name cannot exceed 256 characters');
      }
      
      if (field.value && field.value.length > 1024) {
        throw new ValidationError('Embed field value cannot exceed 1024 characters');
      }
    }
  }
  
  // Calculate total embed length
  const totalLength = 
    (embed.title?.length || 0) +
    (embed.description?.length || 0) +
    (embed.fields?.reduce((sum: number, field: any) => 
      sum + (field.name?.length || 0) + (field.value?.length || 0), 0) || 0);
  
  if (totalLength > 6000) {
    throw new ValidationError('Total embed content cannot exceed 6000 characters');
  }
}
