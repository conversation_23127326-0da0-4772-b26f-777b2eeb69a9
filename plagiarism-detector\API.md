# Plagiarism Detector API Documentation

## Overview

The Plagiarism Detector API provides advanced semantic similarity analysis using multiple embedding models to detect potential plagiarism in text documents. The system uses state-of-the-art AI models including OpenAI's text-embedding-ada-002 and various sentence-transformer models.

## Base URL

```
http://localhost:5000
```

## Authentication

Currently, the API requires an OpenAI API key to be configured in the environment variables. No additional authentication is required for API calls.

## Endpoints

### 1. Health Check

**GET** `/health`

Check the health status of the API and all services.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "services": {
      "textPreprocessor": "operational",
      "embeddingService": "operational",
      "similarityService": "operational",
      "cloneDetectionService": "operational"
    },
    "models": ["text-embedding-ada-002", "all-MiniLM-L6-v2", "all-mpnet-base-v2"]
  }
}
```

### 2. Get Available Models

**GET** `/api/models`

Retrieve information about all available embedding models.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "name": "text-embedding-ada-002",
      "displayName": "OpenAI Ada-002",
      "provider": "openai",
      "dimensions": 1536,
      "maxTokens": 8191,
      "costPerToken": 0.0001,
      "description": "OpenAI's most capable embedding model with high accuracy",
      "strengths": ["High accuracy", "Large context window", "Multilingual support"],
      "limitations": ["Requires API key", "Cost per usage", "Rate limits"],
      "recommendedFor": ["Production use", "High accuracy requirements", "Multilingual content"]
    }
  ]
}
```

### 3. Analyze Texts

**POST** `/api/analyze`

Perform plagiarism detection analysis on multiple text documents.

**Request Body:**
```json
{
  "texts": [
    {
      "id": "text_1",
      "content": "Your text content here...",
      "label": "Document 1",
      "metadata": {}
    },
    {
      "id": "text_2",
      "content": "Another text content...",
      "label": "Document 2"
    }
  ],
  "options": {
    "models": ["text-embedding-ada-002", "all-mpnet-base-v2"],
    "threshold": 0.8,
    "enableCloneDetection": true,
    "enableModelComparison": true,
    "preprocessingOptions": {
      "removeStopWords": false,
      "stemming": false,
      "lemmatization": true,
      "removeNumbers": false,
      "removePunctuation": false,
      "toLowerCase": true,
      "removeExtraWhitespace": true,
      "minWordLength": 1,
      "customStopWords": []
    }
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "requestId": "uuid-here",
    "processedTexts": [...],
    "embeddings": {
      "text-embedding-ada-002": [...],
      "all-mpnet-base-v2": [...]
    },
    "similarityMatrices": {
      "text-embedding-ada-002": {
        "textIds": ["text_1", "text_2"],
        "matrix": [[1.0, 0.85], [0.85, 1.0]],
        "model": "text-embedding-ada-002",
        "threshold": 0.8,
        "metadata": {
          "totalComparisons": 1,
          "averageSimilarity": 0.85,
          "maxSimilarity": 0.85,
          "minSimilarity": 0.85,
          "clonesDetected": 1,
          "processingTime": 2500,
          "timestamp": "2024-01-15T10:30:00.000Z"
        }
      }
    },
    "cloneDetectionResults": [
      {
        "textId1": "text_1",
        "textId2": "text_2",
        "similarity": 0.85,
        "model": "text-embedding-ada-002",
        "confidence": "high",
        "evidence": {
          "commonPhrases": ["shared phrase example"],
          "structuralSimilarity": 0.78,
          "semanticSimilarity": 0.85
        }
      }
    ],
    "modelComparison": {
      "textPairs": [...],
      "modelPerformance": {...},
      "recommendation": {
        "bestModel": "text-embedding-ada-002",
        "reason": "Best balance of accuracy and performance",
        "confidence": 0.8
      }
    },
    "summary": {
      "totalTexts": 2,
      "modelsUsed": ["text-embedding-ada-002", "all-mpnet-base-v2"],
      "processingTime": 5000,
      "clonesFound": 1,
      "averageSimilarity": 0.85,
      "recommendations": ["Found 1 potential plagiarism cases requiring review"]
    },
    "timestamp": "2024-01-15T10:30:00.000Z"
  },
  "metadata": {
    "requestId": "uuid-here",
    "timestamp": "2024-01-15T10:30:00.000Z",
    "processingTime": 5000
  }
}
```

## Request Parameters

### Text Input Object
- `id` (string, optional): Unique identifier for the text
- `content` (string, required): The text content to analyze (min: 10 chars, max: 10,000 chars)
- `label` (string, optional): Human-readable label for the text
- `metadata` (object, optional): Additional metadata

### Analysis Options
- `models` (array, required): List of embedding models to use
- `threshold` (number, 0-1): Similarity threshold for clone detection (default: 0.8)
- `enableCloneDetection` (boolean): Enable advanced clone detection (default: true)
- `enableModelComparison` (boolean): Enable model comparison analysis (default: true)
- `preprocessingOptions` (object): Text preprocessing configuration

### Preprocessing Options
- `removeStopWords` (boolean): Remove common stop words (default: false)
- `stemming` (boolean): Apply word stemming (default: false)
- `lemmatization` (boolean): Apply word lemmatization (default: true)
- `removeNumbers` (boolean): Remove numeric characters (default: false)
- `removePunctuation` (boolean): Remove punctuation marks (default: false)
- `toLowerCase` (boolean): Convert to lowercase (default: true)
- `removeExtraWhitespace` (boolean): Normalize whitespace (default: true)
- `minWordLength` (number): Minimum word length to keep (default: 1)
- `customStopWords` (array): Additional stop words to remove

## Error Responses

### Validation Error (400)
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request data",
    "details": [
      {
        "code": "too_small",
        "minimum": 2,
        "type": "array",
        "inclusive": true,
        "message": "At least 2 texts are required",
        "path": ["texts"]
      }
    ]
  }
}
```

### Rate Limit Error (429)
```json
{
  "success": false,
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Too many requests, please try again later"
  }
}
```

### Server Error (500)
```json
{
  "success": false,
  "error": {
    "code": "ANALYSIS_ERROR",
    "message": "Analysis failed",
    "details": "Specific error details"
  }
}
```

## Rate Limits

- **Analysis endpoint**: 10 requests per minute per IP
- **Other endpoints**: 60 requests per minute per IP

## Usage Examples

### cURL Example
```bash
curl -X POST http://localhost:5000/api/analyze \
  -H "Content-Type: application/json" \
  -d '{
    "texts": [
      {
        "content": "This is the first document to analyze for plagiarism detection.",
        "label": "Document 1"
      },
      {
        "content": "This is the second document that might contain similar content.",
        "label": "Document 2"
      }
    ],
    "options": {
      "models": ["text-embedding-ada-002"],
      "threshold": 0.8,
      "enableCloneDetection": true
    }
  }'
```

### JavaScript Example
```javascript
const response = await fetch('http://localhost:5000/api/analyze', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    texts: [
      {
        content: 'First document content...',
        label: 'Document 1'
      },
      {
        content: 'Second document content...',
        label: 'Document 2'
      }
    ],
    options: {
      models: ['text-embedding-ada-002'],
      threshold: 0.8,
      enableCloneDetection: true,
      enableModelComparison: false
    }
  })
});

const result = await response.json();
console.log(result);
```

## Best Practices

1. **Text Length**: Keep texts between 50-5000 characters for optimal results
2. **Model Selection**: Use OpenAI models for highest accuracy, sentence-transformers for speed
3. **Threshold Setting**: 0.8-0.9 for strict detection, 0.6-0.8 for broader similarity
4. **Batch Size**: Limit to 10 texts per request for optimal performance
5. **Preprocessing**: Enable lemmatization for better semantic understanding

## Supported Models

1. **text-embedding-ada-002** (OpenAI)
   - Dimensions: 1536
   - Best for: High accuracy, production use
   - Cost: $0.0001 per 1K tokens

2. **all-mpnet-base-v2** (Sentence Transformers)
   - Dimensions: 768
   - Best for: Balanced accuracy and speed
   - Cost: Free (local processing)

3. **all-MiniLM-L6-v2** (Sentence Transformers)
   - Dimensions: 384
   - Best for: Fast processing, development
   - Cost: Free (local processing)
