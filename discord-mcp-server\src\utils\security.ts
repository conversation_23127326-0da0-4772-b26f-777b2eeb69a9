import crypto from 'crypto';
import { config } from '../config/index.js';
import { loggers } from './logger.js';

/**
 * Security utilities for encryption, hashing, and secure operations
 */
export class SecurityUtils {
  
  /**
   * Generate a cryptographically secure random string
   */
  static generateSecureToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Generate a secure API key
   */
  static generateApiKey(): string {
    const prefix = 'mcp_';
    const randomPart = this.generateSecureToken(24);
    return `${prefix}${randomPart}`;
  }

  /**
   * Hash a password or sensitive string with salt
   */
  static hashWithSalt(input: string, salt?: string): { hash: string; salt: string } {
    const actualSalt = salt || crypto.randomBytes(16).toString('hex');
    const hash = crypto.pbkdf2Sync(input, actualSalt, 10000, 64, 'sha512').toString('hex');
    
    return { hash, salt: actualSalt };
  }

  /**
   * Verify a hashed value
   */
  static verifyHash(input: string, hash: string, salt: string): boolean {
    const inputHash = crypto.pbkdf2Sync(input, salt, 10000, 64, 'sha512').toString('hex');
    return crypto.timingSafeEqual(Buffer.from(hash, 'hex'), Buffer.from(inputHash, 'hex'));
  }

  /**
   * Encrypt sensitive data
   */
  static encrypt(text: string): { encrypted: string; iv: string } {
    const algorithm = 'aes-256-gcm';
    const key = Buffer.from(config.security.encryptionKey, 'utf8');
    const iv = crypto.randomBytes(16);
    
    const cipher = crypto.createCipher(algorithm, key);
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    return {
      encrypted,
      iv: iv.toString('hex')
    };
  }

  /**
   * Decrypt sensitive data
   */
  static decrypt(encryptedData: { encrypted: string; iv: string }): string {
    const algorithm = 'aes-256-gcm';
    const key = Buffer.from(config.security.encryptionKey, 'utf8');
    const iv = Buffer.from(encryptedData.iv, 'hex');
    
    const decipher = crypto.createDecipher(algorithm, key);
    
    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }

  /**
   * Create a secure hash for API keys
   */
  static hashApiKey(apiKey: string): string {
    return crypto.createHash('sha256')
      .update(apiKey + config.security.encryptionKey)
      .digest('hex');
  }

  /**
   * Validate input against common injection patterns
   */
  static validateInput(input: string): { isValid: boolean; threats: string[] } {
    const threats: string[] = [];
    
    // SQL Injection patterns
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
      /(--|\/\*|\*\/)/,
      /(\b(OR|AND)\s+\d+\s*=\s*\d+)/i,
      /('|\"|;|\\)/
    ];
    
    // XSS patterns
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
      /javascript:/i,
      /on\w+\s*=/i
    ];
    
    // Command injection patterns
    const commandPatterns = [
      /(\||&|;|`|\$\(|\${)/,
      /(rm|del|format|shutdown|reboot)/i
    ];
    
    // Check SQL injection
    for (const pattern of sqlPatterns) {
      if (pattern.test(input)) {
        threats.push('SQL_INJECTION');
        break;
      }
    }
    
    // Check XSS
    for (const pattern of xssPatterns) {
      if (pattern.test(input)) {
        threats.push('XSS');
        break;
      }
    }
    
    // Check command injection
    for (const pattern of commandPatterns) {
      if (pattern.test(input)) {
        threats.push('COMMAND_INJECTION');
        break;
      }
    }
    
    // Check for null bytes
    if (input.includes('\0')) {
      threats.push('NULL_BYTE');
    }
    
    // Check for excessive length (potential DoS)
    if (input.length > 100000) {
      threats.push('EXCESSIVE_LENGTH');
    }
    
    const isValid = threats.length === 0;
    
    if (!isValid) {
      loggers.warn('Security threat detected in input', {
        threats,
        inputLength: input.length,
        inputPreview: input.substring(0, 100)
      });
    }
    
    return { isValid, threats };
  }

  /**
   * Sanitize input string
   */
  static sanitizeInput(input: string): string {
    return input
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // Remove control characters
      .replace(/\0/g, '') // Remove null bytes
      .trim();
  }

  /**
   * Generate a secure session token
   */
  static generateSessionToken(): string {
    const timestamp = Date.now().toString();
    const random = this.generateSecureToken(16);
    const combined = `${timestamp}.${random}`;
    
    return Buffer.from(combined).toString('base64url');
  }

  /**
   * Validate session token
   */
  static validateSessionToken(token: string, maxAge: number = 3600000): boolean {
    try {
      const decoded = Buffer.from(token, 'base64url').toString();
      const [timestamp, random] = decoded.split('.');
      
      if (!timestamp || !random) {
        return false;
      }
      
      const tokenAge = Date.now() - parseInt(timestamp);
      return tokenAge <= maxAge;
      
    } catch (error) {
      loggers.warn('Invalid session token format', { token: token.substring(0, 10) });
      return false;
    }
  }

  /**
   * Create a secure signature for data integrity
   */
  static createSignature(data: string): string {
    return crypto.createHmac('sha256', config.security.encryptionKey)
      .update(data)
      .digest('hex');
  }

  /**
   * Verify data signature
   */
  static verifySignature(data: string, signature: string): boolean {
    const expectedSignature = this.createSignature(data);
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  }

  /**
   * Rate limit key generation
   */
  static generateRateLimitKey(tenantId: string, tool: string, ip?: string): string {
    const components = [tenantId, tool];
    if (ip) {
      components.push(ip);
    }
    return components.join(':');
  }

  /**
   * Mask sensitive data for logging
   */
  static maskSensitiveData(data: any): any {
    if (typeof data === 'string') {
      // Mask potential tokens, keys, passwords
      return data.replace(/(token|key|password|secret)[\s]*[:=][\s]*[^\s,}]+/gi, '$1: ***MASKED***');
    }
    
    if (typeof data === 'object' && data !== null) {
      const masked = { ...data };
      
      for (const [key, value] of Object.entries(masked)) {
        if (typeof key === 'string' && /token|key|password|secret/i.test(key)) {
          masked[key] = '***MASKED***';
        } else if (typeof value === 'object') {
          masked[key] = this.maskSensitiveData(value);
        }
      }
      
      return masked;
    }
    
    return data;
  }

  /**
   * Generate a secure nonce for preventing replay attacks
   */
  static generateNonce(): string {
    const timestamp = Date.now();
    const random = crypto.randomBytes(8).toString('hex');
    return `${timestamp}-${random}`;
  }

  /**
   * Validate nonce (check if it's recent and not reused)
   */
  static validateNonce(nonce: string, usedNonces: Set<string>, maxAge: number = 300000): boolean {
    if (usedNonces.has(nonce)) {
      return false; // Nonce already used
    }
    
    try {
      const [timestampStr] = nonce.split('-');
      const timestamp = parseInt(timestampStr);
      const age = Date.now() - timestamp;
      
      if (age > maxAge) {
        return false; // Nonce too old
      }
      
      usedNonces.add(nonce);
      return true;
      
    } catch (error) {
      return false; // Invalid nonce format
    }
  }
}

export { SecurityUtils };
